"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const prisma_1 = __importDefault(require("../lib/prisma"));
function default_1(fastify) {
    return __awaiter(this, void 0, void 0, function* () {
        // Get user's alerts
        fastify.get('/users/:userId', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { userId } = request.params;
            try {
                const alerts = yield prisma_1.default.alert.findMany({
                    where: { userId }
                });
                return { alerts };
            }
            catch (error) {
                console.error('Error fetching alerts:', error);
                throw new Error('Failed to fetch alerts');
            }
        }));
        // Get specific alert
        fastify.get('/:id', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                const alert = yield prisma_1.default.alert.findUnique({
                    where: { id }
                });
                if (!alert) {
                    reply.code(404).send({ error: 'Alert not found' });
                    return;
                }
                return { alert };
            }
            catch (error) {
                console.error('Error fetching alert:', error);
                throw new Error('Failed to fetch alert');
            }
        }));
        // Create alert
        fastify.post('/', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { userId, type, condition, threshold, tokenSymbol, airdropId } = request.body;
            try {
                const alert = yield prisma_1.default.alert.create({
                    data: {
                        userId,
                        type,
                        condition,
                        threshold,
                        tokenSymbol,
                        airdropId,
                        active: true
                    }
                });
                reply.code(201).send({ alert });
            }
            catch (error) {
                console.error('Error creating alert:', error);
                throw new Error('Failed to create alert');
            }
        }));
        // Update alert
        fastify.put('/:id', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const { condition, threshold, active } = request.body;
            try {
                const alert = yield prisma_1.default.alert.update({
                    where: { id },
                    data: Object.assign(Object.assign(Object.assign({}, (condition && { condition })), (threshold !== undefined && { threshold })), (active !== undefined && { active }))
                });
                return { alert };
            }
            catch (error) {
                console.error('Error updating alert:', error);
                throw new Error('Failed to update alert');
            }
        }));
        // Delete alert
        fastify.delete('/:id', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                yield prisma_1.default.alert.delete({
                    where: { id }
                });
                reply.code(204).send();
            }
            catch (error) {
                console.error('Error deleting alert:', error);
                throw new Error('Failed to delete alert');
            }
        }));
        // Toggle alert active status
        fastify.put('/:id/toggle', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                const alert = yield prisma_1.default.alert.findUnique({
                    where: { id }
                });
                if (!alert) {
                    reply.code(404).send({ error: 'Alert not found' });
                    return;
                }
                const updatedAlert = yield prisma_1.default.alert.update({
                    where: { id },
                    data: { active: !alert.active }
                });
                return { alert: updatedAlert };
            }
            catch (error) {
                console.error('Error toggling alert:', error);
                throw new Error('Failed to toggle alert');
            }
        }));
        // Mark alert as triggered
        fastify.put('/:id/trigger', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                const alert = yield prisma_1.default.alert.update({
                    where: { id },
                    data: { lastTriggered: new Date() }
                });
                return { alert };
            }
            catch (error) {
                console.error('Error marking alert as triggered:', error);
                throw new Error('Failed to mark alert as triggered');
            }
        }));
    });
}
//# sourceMappingURL=alerts.js.map