// Simple auth test utility
import { getRefreshToken } from '../lib/sessionManager';
import { api, initializeAuthHeader } from '../lib/api';

export const testAuth = async () => {
  console.log('=== AUTH TEST START ===');
  
  // Check if we have a refresh token
  const refreshToken = getRefreshToken();
  console.log('Refresh token exists:', !!refreshToken);
  console.log('Refresh token value:', refreshToken ? refreshToken.substring(0, 20) + '...' : 'null');
  
  // Check current Authorization header
  console.log('Current Authorization header:', api.defaults.headers.common['Authorization']);
  
  // Try to initialize auth header
  try {
    const authInitialized = await initializeAuthHeader();
    console.log('Auth header initialization result:', authInitialized);
    console.log('Authorization header after init:', api.defaults.headers.common['Authorization']);
  } catch (error) {
    console.error('Auth header initialization failed:', error);
  }
  
  // Try to make a test API call
  try {
    console.log('Making test API call to /users/me...');
    const response = await api.get('/users/me');
    console.log('Test API call successful:', response.data);
  } catch (error) {
    console.error('Test API call failed:', error);
  }
  
  console.log('=== AUTH TEST END ===');
};

// Export for use in browser console
(window as any).testAuth = testAuth;
