import { Stack } from 'expo-router';

export default function AirdropDetailsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="[id]" 
        options={{ 
          headerShown: true,
          title: 'Airdrop Details',
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="+not-found" 
        options={{ 
          title: 'Not Found',
          headerShown: true 
        }} 
      />
      <Stack.Screen 
        name="alert-form" 
        options={{ 
          title: 'Set Alert',
          headerShown: true,
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="modal" 
        options={{ 
          title: 'Details',
          headerShown: true,
          presentation: 'modal'
        }} 
      />
    </Stack>
  );
}
