import { StyleSheet, TouchableOpacity } from 'react-native';
import { Image } from 'expo-image';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import type { Token } from '@/types/crypto';

interface TokenCardProps {
  token: Token;
  onPress?: () => void;
}

export function TokenCard({ token, onPress }: TokenCardProps) {
  const isPositive = token.change24h >= 0;
  const changeColor = isPositive ? '#34C759' : '#FF3B30';
  const changePrefix = isPositive ? '+' : '';

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <ThemedView style={styles.card}>
        <ThemedView style={styles.left}>
          <Image 
            source={{ uri: token.logo }} 
            style={styles.logo}
            contentFit="contain"
          />
          <ThemedView style={styles.info}>
            <ThemedText type="defaultSemiBold">{token.name}</ThemedText>
            <ThemedText style={styles.symbol}>{token.symbol}</ThemedText>
          </ThemedView>
        </ThemedView>
        
        <ThemedView style={styles.right}>
          <ThemedText type="defaultSemiBold" style={styles.value}>
            ${token.value.toLocaleString()}
          </ThemedText>
          <ThemedText style={[styles.change, { color: changeColor }]}>
            {changePrefix}{token.change24h.toFixed(2)}%
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  left: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  info: {
    flex: 1,
  },
  symbol: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  right: {
    alignItems: 'flex-end',
  },
  value: {
    marginBottom: 2,
  },
  change: {
    fontSize: 12,
    fontWeight: '600',
  },
});
