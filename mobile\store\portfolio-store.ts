import { create } from 'zustand';
import type { Token } from '@/types/crypto';

interface PortfolioState {
  portfolio: Token[];
  totalValue: string;
  totalChange: number;
  loading: boolean;
  error: string | null;
  addToken: (token: Token) => void;
  updateToken: (id: string, updates: Partial<Token>) => void;
  removeToken: (id: string) => void;
  refreshPortfolio: () => Promise<void>;
}

// Mock data for development
const mockPortfolio: Token[] = [
  {
    id: '1',
    name: 'Bitcoin',
    symbol: 'BTC',
    logo: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
    value: 45000,
    change24h: 2.5,
    amount: 0.5,
  },
  {
    id: '2',
    name: 'Ethereum',
    symbol: 'ETH',
    logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
    value: 3200,
    change24h: -1.2,
    amount: 2.3,
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
    logo: 'https://cryptologos.cc/logos/solana-sol-logo.png',
    value: 180,
    change24h: 5.8,
    amount: 10,
  },
  {
    id: '4',
    name: 'Cardano',
    symbol: 'ADA',
    logo: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
    value: 0.65,
    change24h: -0.5,
    amount: 1000,
  },
  {
    id: '5',
    name: 'Polygon',
    symbol: 'MATIC',
    logo: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
    value: 1.2,
    change24h: 3.2,
    amount: 500,
  },
];

const calculateTotalValue = (portfolio: Token[]): string => {
  const total = portfolio.reduce((sum, token) => sum + (token.value * token.amount), 0);
  return `$${total.toLocaleString()}`;
};

const calculateTotalChange = (portfolio: Token[]): number => {
  const totalValue = portfolio.reduce((sum, token) => sum + (token.value * token.amount), 0);
  const totalChange = portfolio.reduce((sum, token) => {
    const tokenValue = token.value * token.amount;
    const weightedChange = (tokenValue / totalValue) * token.change24h;
    return sum + weightedChange;
  }, 0);
  return totalChange;
};

export const usePortfolioStore = create<PortfolioState>((set, get) => ({
  portfolio: mockPortfolio,
  totalValue: calculateTotalValue(mockPortfolio),
  totalChange: calculateTotalChange(mockPortfolio),
  loading: false,
  error: null,

  addToken: (token: Token) => {
    set(state => {
      const newPortfolio = [...state.portfolio, token];
      return {
        portfolio: newPortfolio,
        totalValue: calculateTotalValue(newPortfolio),
        totalChange: calculateTotalChange(newPortfolio),
      };
    });
  },

  updateToken: (id: string, updates: Partial<Token>) => {
    set(state => {
      const newPortfolio = state.portfolio.map(token =>
        token.id === id ? { ...token, ...updates } : token
      );
      return {
        portfolio: newPortfolio,
        totalValue: calculateTotalValue(newPortfolio),
        totalChange: calculateTotalChange(newPortfolio),
      };
    });
  },

  removeToken: (id: string) => {
    set(state => {
      const newPortfolio = state.portfolio.filter(token => token.id !== id);
      return {
        portfolio: newPortfolio,
        totalValue: calculateTotalValue(newPortfolio),
        totalChange: calculateTotalChange(newPortfolio),
      };
    });
  },

  refreshPortfolio: async () => {
    set({ loading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // In a real app, you would fetch updated prices from an API here
      set({ loading: false });
    } catch (error) {
      set({ 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to refresh portfolio' 
      });
    }
  },
}));
