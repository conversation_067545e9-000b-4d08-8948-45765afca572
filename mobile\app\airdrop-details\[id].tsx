import { StyleSheet, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { Image } from 'expo-image';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { StatusBadge } from '@/components/StatusBadge';
import { useAirdropsStore } from '@/store/airdrops-store';

export default function AirdropDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getAirdropById } = useAirdropsStore();
  
  const airdrop = getAirdropById(id!);

  if (!airdrop) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.errorContainer}>
          <ThemedText type="title">Airdrop Not Found</ThemedText>
          <ThemedText>The requested airdrop could not be found.</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  const handleOpenWebsite = () => {
    if (airdrop.website) {
      Linking.openURL(airdrop.website);
    }
  };

  const handleSetAlert = () => {
    router.push(`/airdrop-details/alert-form?id=${airdrop.id}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.content}>
          <ThemedView style={styles.header}>
            <Image 
              source={{ uri: airdrop.logo }} 
              style={styles.logo}
              contentFit="contain"
            />
            <ThemedView style={styles.headerInfo}>
              <ThemedText type="title">{airdrop.name}</ThemedText>
              <ThemedText type="subtitle">{airdrop.symbol}</ThemedText>
              <StatusBadge text={airdrop.status} variant="info" />
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Description</ThemedText>
            <ThemedText>{airdrop.description}</ThemedText>
          </ThemedView>

          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Details</ThemedText>
            <ThemedView style={styles.detailRow}>
              <ThemedText>End Date:</ThemedText>
              <ThemedText type="defaultSemiBold">{airdrop.endDate}</ThemedText>
            </ThemedView>
            <ThemedView style={styles.detailRow}>
              <ThemedText>Estimated Value:</ThemedText>
              <ThemedText type="defaultSemiBold">{airdrop.estimatedValue}</ThemedText>
            </ThemedView>
            <ThemedView style={styles.detailRow}>
              <ThemedText>Requirements:</ThemedText>
              <ThemedText type="defaultSemiBold">{airdrop.requirements}</ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.buttonContainer}>
            {airdrop.website && (
              <TouchableOpacity style={styles.button} onPress={handleOpenWebsite}>
                <ThemedText style={styles.buttonText}>Visit Website</ThemedText>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity style={styles.alertButton} onPress={handleSetAlert}>
              <ThemedText style={styles.alertButtonText}>Set Alert</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'flex-start',
  },
  logo: {
    width: 60,
    height: 60,
    borderRadius: 12,
  },
  headerInfo: {
    flex: 1,
    gap: 4,
  },
  section: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  alertButton: {
    backgroundColor: '#34C759',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  alertButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
