"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_test_1 = require("node:test");
const node_assert_1 = __importDefault(require("node:assert"));
const fastify_1 = __importDefault(require("fastify"));
const prisma_1 = __importDefault(require("../lib/prisma"));
const portfolio_1 = __importDefault(require("../routes/portfolio"));
const auth_1 = __importDefault(require("../routes/auth"));
const jwt_1 = __importDefault(require("@fastify/jwt"));
(0, node_test_1.describe)('Portfolio CRUD Endpoints', () => {
    let app;
    let authToken;
    let testUserId;
    let testHoldingId;
    let testTransactionId;
    (0, node_test_1.before)(() => __awaiter(void 0, void 0, void 0, function* () {
        // Setup test app
        app = (0, fastify_1.default)({ logger: false });
        // Register JWT plugin
        yield app.register(jwt_1.default, {
            secret: 'test-secret-key'
        });
        // Register routes
        yield app.register(auth_1.default, { prefix: '/api/auth' });
        yield app.register(portfolio_1.default, { prefix: '/api/portfolio' });
        yield app.ready();
        // Create test user and get auth token
        const signupResponse = yield app.inject({
            method: 'POST',
            url: '/api/auth/signup',
            payload: {
                email: '<EMAIL>',
                password: 'TestPassword123!',
                name: 'Portfolio Test User'
            }
        });
        node_assert_1.default.strictEqual(signupResponse.statusCode, 201);
        const signupData = JSON.parse(signupResponse.payload);
        testUserId = signupData.user.id;
        // Login to get token
        const loginResponse = yield app.inject({
            method: 'POST',
            url: '/api/auth/login',
            payload: {
                email: '<EMAIL>',
                password: 'TestPassword123!'
            }
        });
        node_assert_1.default.strictEqual(loginResponse.statusCode, 200);
        const loginData = JSON.parse(loginResponse.payload);
        authToken = loginData.accessToken;
    }));
    (0, node_test_1.after)(() => __awaiter(void 0, void 0, void 0, function* () {
        // Cleanup test data
        yield prisma_1.default.transaction.deleteMany({
            where: { holding: { userId: testUserId } }
        });
        yield prisma_1.default.portfolioHolding.deleteMany({
            where: { userId: testUserId }
        });
        yield prisma_1.default.user.delete({
            where: { id: testUserId }
        });
        yield app.close();
    }));
    (0, node_test_1.describe)('Portfolio Summary', () => {
        (0, node_test_1.test)('should get empty portfolio summary', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: '/api/portfolio/summary',
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.summary.holdingsCount, 0);
            node_assert_1.default.strictEqual(data.summary.totalValue, 0);
        }));
    });
    (0, node_test_1.describe)('Holdings CRUD', () => {
        (0, node_test_1.test)('should create a new holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'POST',
                url: '/api/portfolio/holdings',
                headers: {
                    authorization: `Bearer ${authToken}`
                },
                payload: {
                    tokenSymbol: 'btc',
                    tokenName: 'Bitcoin',
                    currentAmount: 0.5
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 201);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Holding created successfully');
            node_assert_1.default.strictEqual(data.holding.tokenSymbol, 'BTC'); // Should be normalized to uppercase
            node_assert_1.default.strictEqual(data.holding.currentAmount, 0.5);
            testHoldingId = data.holding.id;
        }));
        (0, node_test_1.test)('should not create duplicate holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'POST',
                url: '/api/portfolio/holdings',
                headers: {
                    authorization: `Bearer ${authToken}`
                },
                payload: {
                    tokenSymbol: 'BTC',
                    tokenName: 'Bitcoin',
                    currentAmount: 1.0
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 400);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.error, 'Holding for this token already exists');
        }));
        (0, node_test_1.test)('should get all holdings', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: '/api/portfolio/holdings',
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.holdings.length, 1);
            node_assert_1.default.strictEqual(data.holdings[0].tokenSymbol, 'BTC');
        }));
        (0, node_test_1.test)('should get specific holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: `/api/portfolio/holdings/${testHoldingId}`,
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.holding.id, testHoldingId);
            node_assert_1.default.strictEqual(data.holding.tokenSymbol, 'BTC');
        }));
        (0, node_test_1.test)('should update holding amount', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'PUT',
                url: `/api/portfolio/holdings/${testHoldingId}`,
                headers: {
                    authorization: `Bearer ${authToken}`
                },
                payload: {
                    currentAmount: 0.75
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Holding updated successfully');
            node_assert_1.default.strictEqual(data.holding.currentAmount, 0.75);
        }));
    });
    (0, node_test_1.describe)('Transactions CRUD', () => {
        (0, node_test_1.test)('should create a new transaction', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'POST',
                url: '/api/portfolio/transactions',
                headers: {
                    authorization: `Bearer ${authToken}`
                },
                payload: {
                    holdingId: testHoldingId,
                    type: 'BUY',
                    amount: 0.5,
                    pricePerToken: 50000,
                    date: '2024-01-15T10:00:00Z',
                    notes: 'Initial purchase'
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 201);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Transaction added successfully');
            node_assert_1.default.strictEqual(data.transactionType, 'BUY');
        }));
        (0, node_test_1.test)('should get transactions for holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: `/api/portfolio/holdings/${testHoldingId}/transactions`,
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.transactions.length, 1);
            node_assert_1.default.strictEqual(data.transactions[0].type, 'BUY');
            testTransactionId = data.transactions[0].id;
        }));
        (0, node_test_1.test)('should get all user transactions', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: '/api/portfolio/transactions',
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.transactions.length, 1);
            node_assert_1.default.strictEqual(data.transactions[0].holding.tokenSymbol, 'BTC');
        }));
        (0, node_test_1.test)('should delete transaction', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'DELETE',
                url: `/api/portfolio/transactions/${testTransactionId}`,
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Transaction deleted successfully');
        }));
    });
    (0, node_test_1.describe)('Portfolio Management', () => {
        (0, node_test_1.test)('should refresh portfolio prices', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'POST',
                url: '/api/portfolio/refresh-prices',
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Portfolio prices updated successfully');
        }));
        (0, node_test_1.test)('should delete holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'DELETE',
                url: `/api/portfolio/holdings/${testHoldingId}`,
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 200);
            const data = JSON.parse(response.payload);
            node_assert_1.default.strictEqual(data.message, 'Holding and all associated transactions deleted successfully');
        }));
    });
    (0, node_test_1.describe)('Error Handling', () => {
        (0, node_test_1.test)('should return 401 for unauthenticated requests', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: '/api/portfolio/holdings'
            });
            node_assert_1.default.strictEqual(response.statusCode, 401);
        }));
        (0, node_test_1.test)('should return 404 for non-existent holding', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'GET',
                url: '/api/portfolio/holdings/non-existent-id',
                headers: {
                    authorization: `Bearer ${authToken}`
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 404);
        }));
        (0, node_test_1.test)('should validate required fields', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield app.inject({
                method: 'POST',
                url: '/api/portfolio/holdings',
                headers: {
                    authorization: `Bearer ${authToken}`
                },
                payload: {
                    tokenSymbol: 'ETH'
                    // Missing required fields
                }
            });
            node_assert_1.default.strictEqual(response.statusCode, 400);
        }));
    });
});
//# sourceMappingURL=portfolio.test.js.map