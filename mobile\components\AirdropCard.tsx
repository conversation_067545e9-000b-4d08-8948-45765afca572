import { StyleSheet, TouchableOpacity } from 'react-native';
import { Image } from 'expo-image';
import { router } from 'expo-router';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { StatusBadge } from './StatusBadge';
import type { Airdrop } from '@/types/crypto';

interface AirdropCardProps {
  airdrop: Airdrop;
}

export function AirdropCard({ airdrop }: AirdropCardProps) {
  const handlePress = () => {
    router.push(`/airdrop-details/${airdrop.id}`);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <ThemedView style={styles.card}>
        <ThemedView style={styles.header}>
          <Image 
            source={{ uri: airdrop.logo }} 
            style={styles.logo}
            contentFit="contain"
          />
          <ThemedView style={styles.headerInfo}>
            <ThemedText type="defaultSemiBold">{airdrop.name}</ThemedText>
            <ThemedText type="default" style={styles.symbol}>
              {airdrop.symbol}
            </ThemedText>
          </ThemedView>
          <StatusBadge text={airdrop.status} variant="info" />
        </ThemedView>
        
        <ThemedText style={styles.description} numberOfLines={2}>
          {airdrop.description}
        </ThemedText>
        
        <ThemedView style={styles.footer}>
          <ThemedView style={styles.footerItem}>
            <ThemedText type="default" style={styles.label}>
              End Date
            </ThemedText>
            <ThemedText type="defaultSemiBold">
              {airdrop.endDate}
            </ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.footerItem}>
            <ThemedText type="default" style={styles.label}>
              Est. Value
            </ThemedText>
            <ThemedText type="defaultSemiBold">
              {airdrop.estimatedValue}
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 8,
  },
  headerInfo: {
    flex: 1,
  },
  symbol: {
    color: '#666',
    marginTop: 2,
  },
  description: {
    marginBottom: 16,
    lineHeight: 20,
    color: '#666',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerItem: {
    flex: 1,
  },
  label: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
});
