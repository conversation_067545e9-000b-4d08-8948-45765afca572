"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logoutSchema = exports.refreshTokenSchema = exports.loginSchema = exports.signupSchema = void 0;
exports.signupSchema = {
    body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
            email: {
                type: 'string',
                format: 'email',
                minLength: 5,
                maxLength: 255
            },
            password: {
                type: 'string',
                minLength: 8,
                maxLength: 255,
                pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$'
            }
        },
        additionalProperties: false
    }
};
exports.loginSchema = {
    body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
            email: {
                type: 'string',
                format: 'email'
            },
            password: {
                type: 'string',
                minLength: 1
            }
        },
        additionalProperties: false
    }
};
exports.refreshTokenSchema = {
    body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
            refreshToken: {
                type: 'string',
                minLength: 1
            }
        },
        additionalProperties: false
    }
};
exports.logoutSchema = {
    body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
            refreshToken: {
                type: 'string',
                minLength: 1
            }
        },
        additionalProperties: false
    }
};
//# sourceMappingURL=auth.js.map