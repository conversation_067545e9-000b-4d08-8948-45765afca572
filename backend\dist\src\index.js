"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const fastify_1 = __importDefault(require("fastify"));
const prisma_1 = __importDefault(require("./lib/prisma"));
const jwt_1 = __importDefault(require("@fastify/jwt"));
const cors_1 = __importDefault(require("@fastify/cors"));
// Import route handlers
const users_1 = __importDefault(require("./routes/users"));
const auth_1 = __importDefault(require("./routes/auth"));
const airdrops_1 = __importDefault(require("./routes/airdrops"));
const portfolio_1 = __importDefault(require("./routes/portfolio"));
const alerts_1 = __importDefault(require("./routes/alerts"));
// Create Fastify server instance
const server = (0, fastify_1.default)({
    logger: true
});
// Register CORS plugin
server.register(cors_1.default, {
    origin: 'http://localhost:5173', // Explicitly allow frontend origin
    credentials: true,
});
// Register fastify-jwt plugin
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret) {
    server.log.error('JWT_SECRET is not defined in environment variables. Server cannot start securely.');
    process.exit(1);
}
server.register(jwt_1.default, {
    secret: jwtSecret,
    sign: {
        expiresIn: '1h' // Access tokens expire in 1 hour
    },
    verify: {
        maxAge: '1h' // Verify tokens are not older than 1 hour
    }
});
// Define a test route
server.get('/', () => __awaiter(void 0, void 0, void 0, function* () {
    return { hello: 'world' };
}));
// Global hook to ensure CORS headers are always sent
server.addHook('onSend', (request, reply, payload, done) => {
    reply.header('Access-Control-Allow-Origin', 'http://localhost:5173');
    reply.header('Access-Control-Allow-Credentials', 'true');
    done();
});
// Register route handlers
server.register(auth_1.default, { prefix: '/api/auth' });
server.register(users_1.default, { prefix: '/api/users' });
server.register(airdrops_1.default, { prefix: '/api/airdrops' });
server.register(portfolio_1.default, { prefix: '/api/portfolio' });
server.register(alerts_1.default, { prefix: '/api/alerts' });
// Define server startup function
const start = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Get port from environment variable or use default
        const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3001;
        // Start the server
        yield server.listen({ port, host: '0.0.0.0' });
        console.log(`Server is running on port ${port}`);
    }
    catch (err) {
        server.log.error(err);
        process.exit(1);
    }
    finally {
        // Close Prisma client on server shutdown
        process.on('beforeExit', () => __awaiter(void 0, void 0, void 0, function* () {
            yield prisma_1.default.$disconnect();
        }));
    }
});
// Start the server
start();
//# sourceMappingURL=index.js.map