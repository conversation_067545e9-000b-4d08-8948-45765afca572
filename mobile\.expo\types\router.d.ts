/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/airdrops` | `/airdrops`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/alerts` | `/alerts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/portfolio` | `/portfolio`; params?: Router.UnknownInputParams; } | { pathname: `/airdrop-details/alert-form`; params?: Router.UnknownInputParams; } | { pathname: `/airdrop-details/modal`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/airdrop-details/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/airdrop-details/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/airdrops` | `/airdrops`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/alerts` | `/alerts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/portfolio` | `/portfolio`; params?: Router.UnknownOutputParams; } | { pathname: `/airdrop-details/alert-form`; params?: Router.UnknownOutputParams; } | { pathname: `/airdrop-details/modal`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/airdrop-details/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/airdrop-details/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/airdrops${`?${string}` | `#${string}` | ''}` | `/airdrops${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/alerts${`?${string}` | `#${string}` | ''}` | `/alerts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/dashboard${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/portfolio${`?${string}` | `#${string}` | ''}` | `/portfolio${`?${string}` | `#${string}` | ''}` | `/airdrop-details/alert-form${`?${string}` | `#${string}` | ''}` | `/airdrop-details/modal${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/airdrops` | `/airdrops`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/alerts` | `/alerts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/portfolio` | `/portfolio`; params?: Router.UnknownInputParams; } | { pathname: `/airdrop-details/alert-form`; params?: Router.UnknownInputParams; } | { pathname: `/airdrop-details/modal`; params?: Router.UnknownInputParams; } | `/+not-found` | `/airdrop-details/+not-found` | `/airdrop-details/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/airdrop-details/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/airdrop-details/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
