import { StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useState } from 'react';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { TokenCard } from '@/components/TokenCard';
import { SummaryCard } from '@/components/SummaryCard';
import { usePortfolioStore } from '@/store/portfolio-store';

export default function PortfolioScreen() {
  const { portfolio, totalValue, totalChange } = usePortfolioStore();
  const [sortBy, setSortBy] = useState<'value' | 'change' | 'name'>('value');

  const sortedPortfolio = [...portfolio].sort((a, b) => {
    switch (sortBy) {
      case 'value':
        return b.value - a.value;
      case 'change':
        return b.change24h - a.change24h;
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Portfolio</ThemedText>
        <ThemedText type="subtitle">Manage your crypto assets</ThemedText>
      </ThemedView>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.content}>
          <SummaryCard 
            title="Total Portfolio Value"
            value={totalValue}
            change={totalChange}
          />
          
          <ThemedView style={styles.sortContainer}>
            <ThemedText type="defaultSemiBold">Sort by:</ThemedText>
            <ThemedView style={styles.sortButtons}>
              {(['value', 'change', 'name'] as const).map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.sortButton,
                    sortBy === option && styles.sortButtonActive
                  ]}
                  onPress={() => setSortBy(option)}
                >
                  <ThemedText 
                    style={[
                      styles.sortButtonText,
                      sortBy === option && styles.sortButtonTextActive
                    ]}
                  >
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ThemedView>
          </ThemedView>
          
          <ThemedView style={styles.tokenList}>
            {sortedPortfolio.map((token) => (
              <TokenCard key={token.id} token={token} />
            ))}
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 0,
    gap: 20,
  },
  sortContainer: {
    gap: 12,
  },
  sortButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  sortButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  sortButtonActive: {
    backgroundColor: '#007AFF',
  },
  sortButtonText: {
    fontSize: 14,
    color: '#666',
  },
  sortButtonTextActive: {
    color: '#fff',
  },
  tokenList: {
    gap: 12,
  },
});
