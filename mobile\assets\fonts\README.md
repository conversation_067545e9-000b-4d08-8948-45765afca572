# Custom Fonts

This directory contains custom fonts for the Nobify app.

## Required Fonts

The app currently expects:
- `SpaceMono-Regular.ttf` - Used in the main layout

## Adding Custom Fonts

1. **Download fonts** from:
   - Google Fonts: https://fonts.google.com/
   - Font Squirrel: https://www.fontsquirrel.com/
   - Adobe Fonts: https://fonts.adobe.com/

2. **Supported formats**:
   - `.ttf` (TrueType Font)
   - `.otf` (OpenType Font)

3. **Font loading** is handled in `app/_layout.tsx`:
   ```typescript
   const [loaded] = useFonts({
     SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
   });
   ```

## Recommended Fonts for Crypto Apps

### Modern & Clean
- **Inter** - Excellent for UI text
- **Roboto** - Google's material design font
- **SF Pro** - Apple's system font (iOS style)

### Monospace (for numbers/addresses)
- **Space Mono** - Currently used
- **<PERSON><PERSON> Code** - Great for code/addresses
- **JetBrains Mono** - Developer-friendly

### Display/Headers
- **Poppins** - Modern and friendly
- **Montserrat** - Clean and professional
- **Nunito** - Rounded and approachable

## Current Status

🔴 **Missing**: SpaceMono-Regular.ttf
📝 **TODO**: 
- [ ] Download SpaceMono font
- [ ] Add additional fonts for better typography
- [ ] Test font rendering on different devices
