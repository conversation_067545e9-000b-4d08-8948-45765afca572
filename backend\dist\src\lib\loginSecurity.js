"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.recordLoginAttempt = recordLoginAttempt;
exports.checkLoginThrottle = checkLoginThrottle;
exports.cleanupOldAttempts = cleanupOldAttempts;
const prisma_1 = __importDefault(require("./prisma"));
const MAX_FAILED_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds
function recordLoginAttempt(data) {
    return __awaiter(this, void 0, void 0, function* () {
        return prisma_1.default.loginAttempt.create({
            data: {
                email: data.email,
                ipAddress: data.ipAddress,
                success: data.success,
                userId: data.userId
            }
        });
    });
}
function checkLoginThrottle(email, ipAddress, reply) {
    return __awaiter(this, void 0, void 0, function* () {
        const timeWindow = new Date(Date.now() - LOCKOUT_DURATION);
        // Count failed attempts within time window for both email and IP
        const [emailAttempts, ipAttempts] = yield Promise.all([
            prisma_1.default.loginAttempt.count({
                where: {
                    email,
                    success: false,
                    createdAt: {
                        gte: timeWindow
                    }
                }
            }),
            prisma_1.default.loginAttempt.count({
                where: {
                    ipAddress,
                    success: false,
                    createdAt: {
                        gte: timeWindow
                    }
                }
            })
        ]);
        if (emailAttempts >= MAX_FAILED_ATTEMPTS) {
            const minutesLeft = Math.ceil(LOCKOUT_DURATION / 60000);
            reply.code(429).send({
                error: `Too many failed login attempts. Please try again in ${minutesLeft} minutes.`
            });
            return false;
        }
        if (ipAttempts >= MAX_FAILED_ATTEMPTS * 2) {
            const minutesLeft = Math.ceil(LOCKOUT_DURATION / 60000);
            reply.code(429).send({
                error: `Too many failed login attempts from this IP. Please try again in ${minutesLeft} minutes.`
            });
            return false;
        }
        return true;
    });
}
function cleanupOldAttempts() {
    return __awaiter(this, void 0, void 0, function* () {
        const cutoffDate = new Date(Date.now() - LOCKOUT_DURATION * 2);
        yield prisma_1.default.loginAttempt.deleteMany({
            where: {
                createdAt: {
                    lt: cutoffDate
                }
            }
        });
    });
}
//# sourceMappingURL=loginSecurity.js.map