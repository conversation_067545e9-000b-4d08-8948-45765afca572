"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAirdrops = getAirdrops;
exports.getPrices = getPrices;
exports.getSimplePrices = getSimplePrices;
exports.getHistoricalPrice = getHistoricalPrice;
exports.batchUpdatePrices = batchUpdatePrices;
exports.getPriceAlerts = getPriceAlerts;
exports.clearPriceCaches = clearPriceCaches;
exports.getCacheStats = getCacheStats;
const axios_1 = __importDefault(require("axios"));
const node_cache_1 = __importDefault(require("node-cache"));
// Cache configurations
const priceCache = new node_cache_1.default({ stdTTL: 60, checkperiod: 30 }); // Cache prices for 1 minute
const coinListCache = new node_cache_1.default({ stdTTL: 3600, checkperiod: 600 }); // Cache coin list for 1 hour
const airdropCache = new node_cache_1.default({ stdTTL: 300, checkperiod: 60 }); // Cache airdrops for 5 minutes
const COINGECKO_API_URL = process.env.COINGECKO_API_URL || 'https://api.coingecko.com/api/v3';
const COINMARKETCAP_API_URL = process.env.COINMARKETCAP_API_URL || 'https://pro-api.coinmarketcap.com/v1';
// Rate limiting
let lastApiCall = 0;
const API_RATE_LIMIT = 1000; // 1 second between calls for free tier
// Symbol to CoinGecko ID mapping for common tokens
const SYMBOL_TO_ID_MAP = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'BNB': 'binancecoin',
    'ADA': 'cardano',
    'SOL': 'solana',
    'XRP': 'ripple',
    'DOT': 'polkadot',
    'DOGE': 'dogecoin',
    'AVAX': 'avalanche-2',
    'SHIB': 'shiba-inu',
    'MATIC': 'matic-network',
    'LTC': 'litecoin',
    'UNI': 'uniswap',
    'LINK': 'chainlink',
    'ATOM': 'cosmos',
    'XLM': 'stellar',
    'BCH': 'bitcoin-cash',
    'ALGO': 'algorand',
    'VET': 'vechain',
    'ICP': 'internet-computer',
    'FIL': 'filecoin',
    'TRX': 'tron',
    'ETC': 'ethereum-classic',
    'XMR': 'monero',
    'HBAR': 'hedera-hashgraph',
    'APE': 'apecoin',
    'NEAR': 'near',
    'FLOW': 'flow',
    'MANA': 'decentraland',
    'SAND': 'the-sandbox'
};
// Rate limiting helper
function rateLimitedApiCall() {
    return __awaiter(this, void 0, void 0, function* () {
        const now = Date.now();
        const timeSinceLastCall = now - lastApiCall;
        if (timeSinceLastCall < API_RATE_LIMIT) {
            const waitTime = API_RATE_LIMIT - timeSinceLastCall;
            yield new Promise(resolve => setTimeout(resolve, waitTime));
        }
        lastApiCall = Date.now();
    });
}
// Get all coins list for symbol resolution
function getCoinsList() {
    return __awaiter(this, void 0, void 0, function* () {
        const cacheKey = 'coins_list';
        const cachedList = coinListCache.get(cacheKey);
        if (cachedList) {
            return cachedList;
        }
        try {
            yield rateLimitedApiCall();
            console.log('Fetching coins list from CoinGecko API');
            const response = yield axios_1.default.get(`${COINGECKO_API_URL}/coins/list`, {
                timeout: 10000
            });
            const coinsList = response.data.map((coin) => ({
                id: coin.id,
                symbol: coin.symbol.toUpperCase(),
                name: coin.name
            }));
            coinListCache.set(cacheKey, coinsList);
            return coinsList;
        }
        catch (error) {
            console.error('Error fetching coins list:', error);
            return [];
        }
    });
}
// Resolve token symbols to CoinGecko IDs
function resolveSymbolsToIds(symbols) {
    return __awaiter(this, void 0, void 0, function* () {
        const symbolToIdMap = {};
        const unknownSymbols = [];
        // First, try to resolve using the static mapping
        for (const symbol of symbols) {
            const upperSymbol = symbol.toUpperCase();
            if (SYMBOL_TO_ID_MAP[upperSymbol]) {
                symbolToIdMap[upperSymbol] = SYMBOL_TO_ID_MAP[upperSymbol];
            }
            else {
                unknownSymbols.push(upperSymbol);
            }
        }
        // For unknown symbols, search in the coins list
        if (unknownSymbols.length > 0) {
            try {
                const coinsList = yield getCoinsList();
                for (const symbol of unknownSymbols) {
                    const coin = coinsList.find(c => c.symbol === symbol);
                    if (coin) {
                        symbolToIdMap[symbol] = coin.id;
                    }
                    else {
                        console.warn(`Could not resolve symbol: ${symbol}`);
                    }
                }
            }
            catch (error) {
                console.error('Error resolving symbols:', error);
            }
        }
        return symbolToIdMap;
    });
}
function getAirdrops() {
    return __awaiter(this, void 0, void 0, function* () {
        const cacheKey = 'airdrops';
        const cachedAirdrops = airdropCache.get(cacheKey);
        if (cachedAirdrops) {
            console.log('Fetching airdrops from cache');
            return cachedAirdrops;
        }
        let airdrops = [];
        try {
            yield rateLimitedApiCall();
            console.log('Fetching airdrops from CoinGecko API');
            const response = yield axios_1.default.get(`${COINGECKO_API_URL}/search/trending`, {
                timeout: 10000
            });
            airdrops = response.data.coins.map((coin) => ({
                id: coin.item.id,
                name: coin.item.name,
                symbol: coin.item.symbol,
                thumb: coin.item.thumb,
                description: `Trending coin: ${coin.item.name}`,
                status: 'active',
                value: 'N/A',
                endDate: 'N/A'
            }));
            airdropCache.set(cacheKey, airdrops);
            return airdrops;
        }
        catch (error) {
            console.error('Error fetching airdrops:', error);
            return [];
        }
    });
}
// Enhanced price fetching with comprehensive data
function getPrices(tokenSymbols) {
    return __awaiter(this, void 0, void 0, function* () {
        if (tokenSymbols.length === 0) {
            return {};
        }
        // Normalize symbols to uppercase
        const normalizedSymbols = tokenSymbols.map(s => s.toUpperCase());
        const cacheKey = `enhanced_prices_${normalizedSymbols.sort().join(',')}`;
        const cachedPrices = priceCache.get(cacheKey);
        if (cachedPrices) {
            console.log('Fetching enhanced prices from cache');
            return cachedPrices;
        }
        try {
            // Resolve symbols to CoinGecko IDs
            const symbolToIdMap = yield resolveSymbolsToIds(normalizedSymbols);
            const coinIds = Object.values(symbolToIdMap);
            if (coinIds.length === 0) {
                console.warn('No valid coin IDs found for symbols:', normalizedSymbols);
                return {};
            }
            yield rateLimitedApiCall();
            console.log('Fetching enhanced prices from CoinGecko API for:', coinIds);
            const response = yield axios_1.default.get(`${COINGECKO_API_URL}/simple/price`, {
                params: {
                    ids: coinIds.join(','),
                    vs_currencies: 'usd',
                    include_24hr_change: 'true',
                    include_24hr_vol: 'true',
                    include_market_cap: 'true',
                    include_last_updated_at: 'true'
                },
                timeout: 15000
            });
            const priceData = response.data;
            const enhancedPrices = {};
            // Map the response back to symbols
            for (const [symbol, coinId] of Object.entries(symbolToIdMap)) {
                const coinData = priceData[coinId];
                if (coinData) {
                    enhancedPrices[symbol.toLowerCase()] = {
                        usd: coinData.usd || 0,
                        usd_24h_change: coinData.usd_24h_change || 0,
                        usd_24h_vol: coinData.usd_24h_vol || 0,
                        usd_market_cap: coinData.usd_market_cap || 0,
                        last_updated_at: coinData.last_updated_at || Math.floor(Date.now() / 1000)
                    };
                }
                else {
                    console.warn(`No price data found for ${symbol} (${coinId})`);
                }
            }
            priceCache.set(cacheKey, enhancedPrices);
            return enhancedPrices;
        }
        catch (error) {
            console.error('Error fetching enhanced prices:', error);
            // Return fallback data structure for failed requests
            const fallbackPrices = {};
            for (const symbol of normalizedSymbols) {
                fallbackPrices[symbol.toLowerCase()] = {
                    usd: 0,
                    usd_24h_change: 0,
                    usd_24h_vol: 0,
                    usd_market_cap: 0,
                    last_updated_at: Math.floor(Date.now() / 1000)
                };
            }
            return fallbackPrices;
        }
    });
}
// Backward compatibility function for existing code
function getSimplePrices(tokenSymbols) {
    return __awaiter(this, void 0, void 0, function* () {
        const enhancedPrices = yield getPrices(tokenSymbols);
        const simplePrices = {};
        for (const [symbol, data] of Object.entries(enhancedPrices)) {
            simplePrices[symbol] = {
                usd: data.usd,
                usd_24h_change: data.usd_24h_change
            };
        }
        return simplePrices;
    });
}
// Get historical price for a specific date (useful for transaction cost basis)
function getHistoricalPrice(tokenSymbol, date) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c;
        try {
            const symbolToIdMap = yield resolveSymbolsToIds([tokenSymbol.toUpperCase()]);
            const coinId = symbolToIdMap[tokenSymbol.toUpperCase()];
            if (!coinId) {
                console.warn(`Could not resolve symbol: ${tokenSymbol}`);
                return null;
            }
            // Format date as DD-MM-YYYY for CoinGecko API
            const formattedDate = date.toLocaleDateString('en-GB');
            yield rateLimitedApiCall();
            console.log(`Fetching historical price for ${tokenSymbol} on ${formattedDate}`);
            const response = yield axios_1.default.get(`${COINGECKO_API_URL}/coins/${coinId}/history`, {
                params: {
                    date: formattedDate,
                    localization: false
                },
                timeout: 15000
            });
            const price = (_c = (_b = (_a = response.data) === null || _a === void 0 ? void 0 : _a.market_data) === null || _b === void 0 ? void 0 : _b.current_price) === null || _c === void 0 ? void 0 : _c.usd;
            return price || null;
        }
        catch (error) {
            console.error(`Error fetching historical price for ${tokenSymbol}:`, error);
            return null;
        }
    });
}
// Batch price updates with error handling and retry logic
function batchUpdatePrices(tokenSymbols_1) {
    return __awaiter(this, arguments, void 0, function* (tokenSymbols, maxRetries = 3) {
        let attempt = 0;
        let lastError = null;
        while (attempt < maxRetries) {
            try {
                const prices = yield getPrices(tokenSymbols);
                // Check if we got valid data for at least some tokens
                const validPrices = Object.values(prices).filter(p => p.usd > 0);
                if (validPrices.length > 0) {
                    return prices;
                }
                throw new Error('No valid price data received');
            }
            catch (error) {
                lastError = error;
                attempt++;
                if (attempt < maxRetries) {
                    const backoffTime = Math.pow(2, attempt) * 1000; // Exponential backoff
                    console.log(`Price fetch attempt ${attempt} failed, retrying in ${backoffTime}ms...`);
                    yield new Promise(resolve => setTimeout(resolve, backoffTime));
                }
            }
        }
        console.error(`Failed to fetch prices after ${maxRetries} attempts:`, lastError);
        // Return fallback data
        const fallbackPrices = {};
        for (const symbol of tokenSymbols) {
            fallbackPrices[symbol.toLowerCase()] = {
                usd: 0,
                usd_24h_change: 0,
                usd_24h_vol: 0,
                usd_market_cap: 0,
                last_updated_at: Math.floor(Date.now() / 1000)
            };
        }
        return fallbackPrices;
    });
}
// Get price alerts data (price targets, significant changes)
function getPriceAlerts(tokenSymbols_1) {
    return __awaiter(this, arguments, void 0, function* (tokenSymbols, thresholdPercentage = 5) {
        try {
            const prices = yield getPrices(tokenSymbols);
            const alerts = [];
            for (const [symbol, data] of Object.entries(prices)) {
                const change24h = Math.abs(data.usd_24h_change);
                const isSignificantChange = change24h >= thresholdPercentage;
                let alertType = 'stable';
                if (isSignificantChange) {
                    alertType = data.usd_24h_change > 0 ? 'gain' : 'loss';
                }
                alerts.push({
                    symbol: symbol.toUpperCase(),
                    currentPrice: data.usd,
                    change24h: data.usd_24h_change,
                    isSignificantChange,
                    alertType
                });
            }
            return alerts;
        }
        catch (error) {
            console.error('Error generating price alerts:', error);
            return [];
        }
    });
}
// Clear all caches (useful for testing or manual refresh)
function clearPriceCaches() {
    priceCache.flushAll();
    coinListCache.flushAll();
    airdropCache.flushAll();
    console.log('All price caches cleared');
}
// Get cache statistics
function getCacheStats() {
    return {
        priceCache: {
            keys: priceCache.keys().length,
            hits: priceCache.getStats().hits,
            misses: priceCache.getStats().misses
        },
        coinListCache: {
            keys: coinListCache.keys().length,
            hits: coinListCache.getStats().hits,
            misses: coinListCache.getStats().misses
        },
        airdropCache: {
            keys: airdropCache.keys().length,
            hits: airdropCache.getStats().hits,
            misses: airdropCache.getStats().misses
        }
    };
}
//# sourceMappingURL=cryptoApi.js.map