import { StyleSheet, TouchableOpacity, TextInput, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { useState } from 'react';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAlertsStore } from '@/store/alerts-store';
import { useAirdropsStore } from '@/store/airdrops-store';

export default function AlertFormScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getAirdropById } = useAirdropsStore();
  const { addAlert } = useAlertsStore();
  
  const [alertType, setAlertType] = useState<'deadline' | 'status' | 'custom'>('deadline');
  const [customMessage, setCustomMessage] = useState('');
  
  const airdrop = getAirdropById(id!);

  if (!airdrop) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.errorContainer}>
          <ThemedText type="title">Airdrop Not Found</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  const handleCreateAlert = () => {
    let message = '';
    
    switch (alertType) {
      case 'deadline':
        message = `Reminder: ${airdrop.name} airdrop ends soon!`;
        break;
      case 'status':
        message = `${airdrop.name} airdrop status has been updated`;
        break;
      case 'custom':
        message = customMessage.trim();
        break;
    }

    if (!message) {
      Alert.alert('Error', 'Please enter a custom message');
      return;
    }

    addAlert({
      id: Date.now().toString(),
      title: `${airdrop.name} Alert`,
      message,
      timestamp: new Date().toISOString(),
      read: false,
      type: 'airdrop',
      airdropId: airdrop.id,
    });

    Alert.alert('Success', 'Alert created successfully!', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Alert Type</ThemedText>
          
          {(['deadline', 'status', 'custom'] as const).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.optionButton,
                alertType === type && styles.optionButtonActive
              ]}
              onPress={() => setAlertType(type)}
            >
              <ThemedText 
                style={[
                  styles.optionText,
                  alertType === type && styles.optionTextActive
                ]}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)} Alert
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ThemedView>

        {alertType === 'custom' && (
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Custom Message</ThemedText>
            <TextInput
              style={styles.textInput}
              placeholder="Enter your custom alert message..."
              value={customMessage}
              onChangeText={setCustomMessage}
              multiline
              numberOfLines={3}
            />
          </ThemedView>
        )}

        <TouchableOpacity style={styles.createButton} onPress={handleCreateAlert}>
          <ThemedText style={styles.createButtonText}>Create Alert</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  section: {
    gap: 12,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  optionButtonActive: {
    backgroundColor: '#e3f2fd',
    borderColor: '#007AFF',
  },
  optionText: {
    fontSize: 16,
    color: '#666',
  },
  optionTextActive: {
    color: '#007AFF',
    fontWeight: '600',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  createButton: {
    backgroundColor: '#34C759',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
