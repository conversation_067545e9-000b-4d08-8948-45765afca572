"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateHoldingMetrics = calculateHoldingMetrics;
exports.updateHoldingPrices = updateHoldingPrices;
exports.getPortfolioSummary = getPortfolioSummary;
exports.getHoldingsWithMetrics = getHoldingsWithMetrics;
exports.addTransaction = addTransaction;
exports.addTransactionWithPriceLookup = addTransactionWithPriceLookup;
exports.bulkUpdateAllUserPrices = bulkUpdateAllUserPrices;
exports.getPortfolioAnalytics = getPortfolioAnalytics;
const prisma_1 = __importDefault(require("../lib/prisma"));
const cryptoApi_1 = require("../lib/cryptoApi");
const prisma_2 = require("../generated/prisma");
/**
 * Calculate portfolio metrics for a holding based on its transactions
 */
function calculateHoldingMetrics(holdingId) {
    return __awaiter(this, void 0, void 0, function* () {
        const holding = yield prisma_1.default.portfolioHolding.findUnique({
            where: { id: holdingId },
            include: { transactions: true }
        });
        if (!holding) {
            throw new Error('Holding not found');
        }
        let totalCostBasis = 0;
        let totalAmount = 0;
        let realizedPnL = 0;
        // Calculate metrics from transactions
        for (const transaction of holding.transactions) {
            const amount = transaction.amount;
            const price = transaction.pricePerToken || 0;
            const value = transaction.totalValue || (amount * price);
            switch (transaction.type) {
                case prisma_2.TransactionType.BUY:
                case prisma_2.TransactionType.TRANSFER_IN:
                case prisma_2.TransactionType.AIRDROP:
                case prisma_2.TransactionType.REWARD:
                    totalAmount += amount;
                    totalCostBasis += value;
                    break;
                case prisma_2.TransactionType.SELL:
                case prisma_2.TransactionType.TRANSFER_OUT:
                    totalAmount -= amount;
                    // Calculate realized P&L for sells
                    if (transaction.type === prisma_2.TransactionType.SELL && totalCostBasis > 0) {
                        const avgCostBasis = totalCostBasis / (totalAmount + amount); // Amount before this transaction
                        const costOfSold = avgCostBasis * amount;
                        realizedPnL += (value - costOfSold);
                        totalCostBasis -= costOfSold;
                    }
                    break;
                case prisma_2.TransactionType.STAKE:
                case prisma_2.TransactionType.UNSTAKE:
                    // For staking, we don't change the cost basis, just track the amount
                    if (transaction.type === prisma_2.TransactionType.STAKE) {
                        // Amount is locked but still owned
                    }
                    else {
                        // Amount is unlocked
                    }
                    break;
            }
        }
        const averageCostBasis = totalAmount > 0 ? totalCostBasis / totalAmount : null;
        // Update the holding with calculated metrics
        yield prisma_1.default.portfolioHolding.update({
            where: { id: holdingId },
            data: {
                currentAmount: totalAmount,
                averageCostBasis,
                totalCostBasis,
                realizedPnL,
                updatedAt: new Date()
            }
        });
    });
}
/**
 * Update current prices and calculate unrealized P&L for holdings
 */
function updateHoldingPrices(userId_1) {
    return __awaiter(this, arguments, void 0, function* (userId, useRetry = true) {
        const holdings = yield prisma_1.default.portfolioHolding.findMany({
            where: { userId }
        });
        if (holdings.length === 0) {
            return { updated: 0, failed: 0, errors: [] };
        }
        const tokenSymbols = holdings.map(h => h.tokenSymbol);
        const errors = [];
        let updated = 0;
        let failed = 0;
        try {
            // Use batch update with retry logic for better reliability
            const prices = useRetry
                ? yield (0, cryptoApi_1.batchUpdatePrices)(tokenSymbols, 3)
                : yield (0, cryptoApi_1.getPrices)(tokenSymbols);
            // Update each holding with current price and calculated values
            for (const holding of holdings) {
                try {
                    const priceData = prices[holding.tokenSymbol.toLowerCase()];
                    if (!priceData) {
                        errors.push(`No price data found for ${holding.tokenSymbol}`);
                        failed++;
                        continue;
                    }
                    const currentPrice = priceData.usd;
                    let currentValue = null;
                    let unrealizedPnL = null;
                    let percentageChange = null;
                    if (currentPrice > 0 && holding.currentAmount > 0) {
                        currentValue = holding.currentAmount * currentPrice;
                        if (holding.totalCostBasis && holding.totalCostBasis > 0) {
                            unrealizedPnL = currentValue - holding.totalCostBasis;
                            percentageChange = (unrealizedPnL / holding.totalCostBasis) * 100;
                        }
                    }
                    yield prisma_1.default.portfolioHolding.update({
                        where: { id: holding.id },
                        data: {
                            currentPrice: currentPrice > 0 ? currentPrice : null,
                            currentValue,
                            unrealizedPnL,
                            percentageChange,
                            lastPriceUpdate: new Date(),
                            updatedAt: new Date()
                        }
                    });
                    updated++;
                }
                catch (error) {
                    const errorMsg = `Failed to update ${holding.tokenSymbol}: ${error}`;
                    errors.push(errorMsg);
                    console.error(errorMsg);
                    failed++;
                }
            }
        }
        catch (error) {
            const errorMsg = `Failed to fetch prices: ${error}`;
            errors.push(errorMsg);
            console.error(errorMsg);
            failed = holdings.length;
        }
        return { updated, failed, errors };
    });
}
/**
 * Get portfolio summary with aggregated metrics
 */
function getPortfolioSummary(userId) {
    return __awaiter(this, void 0, void 0, function* () {
        // First update prices and get update statistics
        const priceUpdateStats = yield updateHoldingPrices(userId);
        const holdings = yield prisma_1.default.portfolioHolding.findMany({
            where: { userId }
        });
        let totalValue = 0;
        let totalCostBasis = 0;
        let totalUnrealizedPnL = 0;
        let totalRealizedPnL = 0;
        for (const holding of holdings) {
            totalValue += holding.currentValue || 0;
            totalCostBasis += holding.totalCostBasis || 0;
            totalUnrealizedPnL += holding.unrealizedPnL || 0;
            totalRealizedPnL += holding.realizedPnL || 0;
        }
        const totalPercentageChange = totalCostBasis > 0
            ? (totalUnrealizedPnL / totalCostBasis) * 100
            : 0;
        return {
            totalValue,
            totalCostBasis,
            totalUnrealizedPnL,
            totalRealizedPnL,
            totalPercentageChange,
            holdingsCount: holdings.length,
            priceUpdateStats
        };
    });
}
/**
 * Get holdings with updated metrics
 */
function getHoldingsWithMetrics(userId) {
    return __awaiter(this, void 0, void 0, function* () {
        // Update prices first
        yield updateHoldingPrices(userId);
        const holdings = yield prisma_1.default.portfolioHolding.findMany({
            where: { userId },
            orderBy: [
                { currentValue: 'desc' },
                { tokenSymbol: 'asc' }
            ]
        });
        return holdings;
    });
}
/**
 * Add a new transaction and recalculate holding metrics
 */
function addTransaction(holdingId, transactionData) {
    return __awaiter(this, void 0, void 0, function* () {
        // Create the transaction
        yield prisma_1.default.transaction.create({
            data: Object.assign(Object.assign({ holdingId }, transactionData), { updatedAt: new Date() })
        });
        // Recalculate holding metrics
        yield calculateHoldingMetrics(holdingId);
    });
}
/**
 * Enhanced transaction creation with historical price lookup
 */
function addTransactionWithPriceLookup(holdingId, transactionData) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Get holding info for price lookup
            const holding = yield prisma_1.default.portfolioHolding.findUnique({
                where: { id: holdingId }
            });
            if (!holding) {
                return { success: false, message: 'Holding not found' };
            }
            let finalTransactionData = Object.assign({}, transactionData);
            // If no price provided, try to fetch historical price
            if (!transactionData.pricePerToken && !transactionData.totalValue) {
                console.log(`Attempting to fetch historical price for ${holding.tokenSymbol} on ${transactionData.date}`);
                const historicalPrice = yield (0, cryptoApi_1.getHistoricalPrice)(holding.tokenSymbol, transactionData.date);
                if (historicalPrice) {
                    finalTransactionData.pricePerToken = historicalPrice;
                    finalTransactionData.totalValue = historicalPrice * transactionData.amount;
                    yield addTransaction(holdingId, finalTransactionData);
                    return {
                        success: true,
                        historicalPrice,
                        message: `Transaction added with historical price: $${historicalPrice.toFixed(2)}`
                    };
                }
                else {
                    // Still create transaction without price data
                    yield addTransaction(holdingId, finalTransactionData);
                    return {
                        success: true,
                        message: 'Transaction added without price data (historical price not available)'
                    };
                }
            }
            else {
                // Create transaction with provided price data
                yield addTransaction(holdingId, finalTransactionData);
                return {
                    success: true,
                    message: 'Transaction added successfully'
                };
            }
        }
        catch (error) {
            console.error('Error adding transaction with price lookup:', error);
            return {
                success: false,
                message: `Failed to add transaction: ${error}`
            };
        }
    });
}
/**
 * Bulk price update for all users (useful for scheduled tasks)
 */
function bulkUpdateAllUserPrices() {
    return __awaiter(this, void 0, void 0, function* () {
        const startTime = Date.now();
        let usersProcessed = 0;
        let totalHoldingsUpdated = 0;
        let totalErrors = 0;
        try {
            // Get all users with holdings
            const usersWithHoldings = yield prisma_1.default.user.findMany({
                where: {
                    holdings: {
                        some: {}
                    }
                },
                select: { id: true }
            });
            console.log(`Starting bulk price update for ${usersWithHoldings.length} users`);
            for (const user of usersWithHoldings) {
                try {
                    const result = yield updateHoldingPrices(user.id, true);
                    totalHoldingsUpdated += result.updated;
                    totalErrors += result.failed;
                    usersProcessed++;
                    // Add small delay to avoid rate limiting
                    yield new Promise(resolve => setTimeout(resolve, 100));
                }
                catch (error) {
                    console.error(`Failed to update prices for user ${user.id}:`, error);
                    totalErrors++;
                }
            }
            const processingTime = Date.now() - startTime;
            console.log(`Bulk price update completed: ${usersProcessed} users, ${totalHoldingsUpdated} holdings updated, ${totalErrors} errors, ${processingTime}ms`);
            return {
                usersProcessed,
                totalHoldingsUpdated,
                totalErrors,
                processingTime
            };
        }
        catch (error) {
            console.error('Error in bulk price update:', error);
            return {
                usersProcessed,
                totalHoldingsUpdated,
                totalErrors: totalErrors + 1,
                processingTime: Date.now() - startTime
            };
        }
    });
}
/**
 * Get portfolio performance analytics
 */
function getPortfolioAnalytics(userId) {
    return __awaiter(this, void 0, void 0, function* () {
        // Get updated portfolio summary
        const summary = yield getPortfolioSummary(userId);
        // Get holdings with performance data
        const holdings = yield getHoldingsWithMetrics(userId);
        // Get recent transactions
        const recentTransactions = yield prisma_1.default.transaction.findMany({
            where: {
                holding: { userId }
            },
            include: {
                holding: {
                    select: { tokenSymbol: true }
                }
            },
            orderBy: { date: 'desc' },
            take: 10
        });
        // Calculate top and worst performers
        const holdingsWithPerformance = holdings
            .filter(h => h.percentageChange !== null && h.currentValue && h.currentValue > 0)
            .map(h => ({
            symbol: h.tokenSymbol,
            percentageChange: h.percentageChange,
            value: h.currentValue
        }));
        const topPerformers = holdingsWithPerformance
            .sort((a, b) => b.percentageChange - a.percentageChange)
            .slice(0, 5);
        const worstPerformers = holdingsWithPerformance
            .sort((a, b) => a.percentageChange - b.percentageChange)
            .slice(0, 5);
        // Calculate allocation by value
        const totalPortfolioValue = holdings.reduce((sum, h) => sum + (h.currentValue || 0), 0);
        const allocationByValue = holdings
            .filter(h => h.currentValue && h.currentValue > 0)
            .map(h => ({
            symbol: h.tokenSymbol,
            value: h.currentValue,
            percentage: totalPortfolioValue > 0 ? (h.currentValue / totalPortfolioValue) * 100 : 0
        }))
            .sort((a, b) => b.value - a.value);
        // Format recent activity
        const recentActivity = recentTransactions.map(t => ({
            type: t.type,
            symbol: t.holding.tokenSymbol,
            amount: t.amount,
            date: t.date
        }));
        return {
            summary,
            topPerformers,
            worstPerformers,
            allocationByValue,
            recentActivity
        };
    });
}
//# sourceMappingURL=portfolioService.js.map