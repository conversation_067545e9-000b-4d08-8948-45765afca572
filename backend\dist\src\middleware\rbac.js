"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireRole = requireRole;
exports.requireAnyRole = requireAnyRole;
exports.hasRole = hasRole;
// Define role hierarchy
const roleHierarchy = {
    ADMIN: ['ADMIN', 'MODERATOR', 'USER'],
    MODERATOR: ['MODERATOR', 'USER'],
    USER: ['USER']
};
// Check if a role has permission over another role
function hasRolePermission(requiredRole, userRole) {
    var _a;
    return ((_a = roleHierarchy[userRole]) === null || _a === void 0 ? void 0 : _a.includes(requiredRole)) || false;
}
// Middleware to check if user has required role
function requireRole(requiredRole) {
    return (request, reply) => __awaiter(this, void 0, void 0, function* () {
        try {
            const user = request.user;
            if (!user) {
                reply.code(401).send({ error: 'Unauthorized' });
                return;
            }
            if (!hasRolePermission(requiredRole, user.role)) {
                reply.code(403).send({ error: 'Insufficient permissions' });
                return;
            }
        }
        catch (error) {
            console.error('Role verification error:', error);
            reply.code(500).send({ error: 'Internal server error' });
        }
    });
}
// Helper function to require multiple roles (any of them)
function requireAnyRole(roles) {
    return (request, reply) => __awaiter(this, void 0, void 0, function* () {
        try {
            const user = request.user;
            if (!user) {
                reply.code(401).send({ error: 'Unauthorized' });
                return;
            }
            const hasPermission = roles.some(role => hasRolePermission(role, user.role));
            if (!hasPermission) {
                reply.code(403).send({ error: 'Insufficient permissions' });
                return;
            }
        }
        catch (error) {
            console.error('Role verification error:', error);
            reply.code(500).send({ error: 'Internal server error' });
        }
    });
}
// Helper function to check if user has specific role
function hasRole(user, requiredRole) {
    return hasRolePermission(requiredRole, user.role);
}
//# sourceMappingURL=rbac.js.map