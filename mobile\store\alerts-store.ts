import { create } from 'zustand';
import type { Alert } from '@/types/crypto';

interface AlertsState {
  alerts: Alert[];
  loading: boolean;
  error: string | null;
  addAlert: (alert: Alert) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeAlert: (id: string) => void;
  getUnreadCount: () => number;
}

// Mock data for development
const mockAlerts: Alert[] = [
  {
    id: '1',
    title: 'LayerZero Airdrop Alert',
    message: 'LayerZero airdrop deadline is approaching! Make sure to complete your transactions.',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    read: false,
    type: 'airdrop',
    airdropId: '1',
  },
  {
    id: '2',
    title: 'Market Update',
    message: 'Bitcoin has reached a new all-time high! Consider reviewing your portfolio allocation.',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    read: false,
    type: 'market',
  },
  {
    id: '3',
    title: 'zkSync Era Update',
    message: 'New features have been added to zkSync Era. Check out the latest improvements.',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    read: true,
    type: 'airdrop',
    airdropId: '2',
  },
];

export const useAlertsStore = create<AlertsState>((set, get) => ({
  alerts: mockAlerts,
  loading: false,
  error: null,

  addAlert: (alert: Alert) => {
    set(state => ({
      alerts: [alert, ...state.alerts]
    }));
  },

  markAsRead: (id: string) => {
    set(state => ({
      alerts: state.alerts.map(alert =>
        alert.id === id ? { ...alert, read: true } : alert
      )
    }));
  },

  markAllAsRead: () => {
    set(state => ({
      alerts: state.alerts.map(alert => ({ ...alert, read: true }))
    }));
  },

  removeAlert: (id: string) => {
    set(state => ({
      alerts: state.alerts.filter(alert => alert.id !== id)
    }));
  },

  getUnreadCount: () => {
    return get().alerts.filter(alert => !alert.read).length;
  },
}));
