import { StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { StatusBadge } from './StatusBadge';
import type { Alert } from '@/types/crypto';

interface AlertCardProps {
  alert: Alert;
  onPress?: () => void;
}

export function AlertCard({ alert, onPress }: AlertCardProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <ThemedView style={[styles.card, !alert.read && styles.unreadCard]}>
        <ThemedView style={styles.header}>
          <ThemedText type="defaultSemiBold" style={styles.title}>
            {alert.title}
          </ThemedText>
          <ThemedView style={styles.headerRight}>
            {!alert.read && <StatusBadge text="New" variant="warning" />}
            <ThemedText style={styles.time}>
              {formatTime(alert.timestamp)}
            </ThemedText>
          </ThemedView>
        </ThemedView>
        
        <ThemedText style={styles.message} numberOfLines={3}>
          {alert.message}
        </ThemedText>
        
        <ThemedText style={styles.date}>
          {formatDate(alert.timestamp)}
        </ThemedText>
      </ThemedView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadCard: {
    backgroundColor: '#f8f9ff',
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    flex: 1,
    marginRight: 12,
  },
  headerRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  time: {
    fontSize: 12,
    color: '#666',
  },
  message: {
    marginBottom: 8,
    lineHeight: 18,
    color: '#333',
  },
  date: {
    fontSize: 12,
    color: '#999',
  },
});
