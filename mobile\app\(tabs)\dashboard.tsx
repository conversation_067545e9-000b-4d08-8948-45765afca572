import { StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { SummaryCard } from '@/components/SummaryCard';
import { TokenCard } from '@/components/TokenCard';
import { usePortfolioStore } from '@/store/portfolio-store';

export default function DashboardScreen() {
  const { portfolio, totalValue, totalChange } = usePortfolioStore();

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Dashboard</ThemedText>
        <ThemedText type="subtitle">Your crypto overview</ThemedText>
      </ThemedView>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.content}>
          <SummaryCard 
            title="Total Portfolio Value"
            value={totalValue}
            change={totalChange}
          />
          
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Top Holdings
            </ThemedText>
            {portfolio.slice(0, 5).map((token) => (
              <TokenCard key={token.id} token={token} />
            ))}
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 0,
    gap: 20,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    marginBottom: 8,
  },
});
