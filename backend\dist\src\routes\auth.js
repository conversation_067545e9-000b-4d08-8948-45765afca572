"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const auth_1 = require("../lib/auth");
const loginSecurity_1 = require("../lib/loginSecurity");
const prisma_1 = __importDefault(require("../lib/prisma"));
const auth_2 = require("../schemas/auth");
function default_1(fastify) {
    return __awaiter(this, void 0, void 0, function* () {
        // Signup endpoint
        fastify.post('/signup', {
            schema: auth_2.signupSchema
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { email, password } = request.body;
            try {
                // Check if user already exists
                const existingUser = yield prisma_1.default.user.findUnique({
                    where: { email }
                });
                if (existingUser) {
                    reply.code(400).send({ error: 'Email already registered' });
                    return;
                }
                // Hash password and create user with default USER role
                const hashedPassword = yield (0, auth_1.hashPassword)(password);
                const user = yield prisma_1.default.user.create({
                    data: {
                        email,
                        passwordHash: hashedPassword,
                        role: 'USER'
                    },
                    select: {
                        id: true,
                        email: true,
                        role: true,
                        createdAt: true
                    }
                });
                // Generate tokens
                const authToken = (0, auth_1.generateAuthToken)(fastify, user.id);
                const refreshToken = (0, auth_1.generateRefreshToken)(fastify, user.id);
                // Store refresh token in database with expiration
                const expiresAt = new Date();
                expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now
                yield prisma_1.default.refreshToken.create({
                    data: {
                        token: refreshToken,
                        userId: user.id,
                        expiresAt
                    }
                });
                reply.code(201).send({
                    user,
                    tokens: {
                        accessToken: authToken,
                        refreshToken
                    }
                });
            }
            catch (error) {
                console.error('Signup error:', error);
                reply.code(500).send({ error: 'Internal server error' });
            }
        }));
        // Login endpoint
        fastify.post('/login', {
            schema: auth_2.loginSchema
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { email, password } = request.body;
            try {
                // Get IP address from request
                const ipAddress = request.ip;
                // Check for too many failed attempts
                const canProceed = yield (0, loginSecurity_1.checkLoginThrottle)(email, ipAddress, reply);
                if (!canProceed) {
                    return;
                }
                // Find user
                const user = yield prisma_1.default.user.findUnique({
                    where: { email }
                });
                // Record the attempt regardless of success
                const attemptData = {
                    email,
                    ipAddress,
                    success: false,
                    userId: user === null || user === void 0 ? void 0 : user.id
                };
                if (!user) {
                    yield (0, loginSecurity_1.recordLoginAttempt)(attemptData);
                    reply.code(401).send({ error: 'Invalid credentials' });
                    return;
                }
                // Verify password
                const isValid = yield (0, auth_1.verifyPassword)(password, user.passwordHash);
                if (!isValid) {
                    yield (0, loginSecurity_1.recordLoginAttempt)(attemptData);
                    reply.code(401).send({ error: 'Invalid credentials' });
                    return;
                }
                // Update attempt as successful
                yield (0, loginSecurity_1.recordLoginAttempt)(Object.assign(Object.assign({}, attemptData), { success: true }));
                // Clean up old attempts periodically (1% chance)
                if (Math.random() < 0.01) {
                    (0, loginSecurity_1.cleanupOldAttempts)().catch(console.error);
                }
                // Generate tokens
                const authToken = (0, auth_1.generateAuthToken)(fastify, user.id);
                const refreshToken = (0, auth_1.generateRefreshToken)(fastify, user.id);
                // Store refresh token in database with expiration
                const expiresAt = new Date();
                expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now
                yield prisma_1.default.refreshToken.create({
                    data: {
                        token: refreshToken,
                        userId: user.id,
                        expiresAt
                    }
                });
                reply.send({
                    user: {
                        id: user.id,
                        email: user.email,
                        role: user.role,
                        createdAt: user.createdAt
                    },
                    tokens: {
                        accessToken: authToken,
                        refreshToken
                    }
                });
            }
            catch (error) {
                console.error('Login error:', error);
                reply.code(500).send({ error: 'Internal server error' });
            }
        }));
        // Refresh token endpoint
        fastify.post('/refresh-token', {
            schema: auth_2.refreshTokenSchema
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { refreshToken } = request.body;
            try {
                // Verify refresh token
                const decoded = fastify.jwt.verify(refreshToken);
                if (decoded.type !== 'refresh') {
                    reply.code(401).send({ error: 'Invalid refresh token' });
                    return;
                }
                // Check if refresh token exists and is not expired
                const storedToken = yield prisma_1.default.refreshToken.findUnique({
                    where: { token: refreshToken }
                });
                if (!storedToken || storedToken.expiresAt < new Date()) {
                    // Delete expired token if it exists
                    if (storedToken) {
                        yield prisma_1.default.refreshToken.delete({
                            where: { token: refreshToken }
                        });
                    }
                    reply.code(401).send({ error: 'Invalid or expired refresh token' });
                    return;
                }
                // Generate new tokens
                const newAuthToken = (0, auth_1.generateAuthToken)(fastify, decoded.userId);
                const newRefreshToken = (0, auth_1.generateRefreshToken)(fastify, decoded.userId);
                // Replace old refresh token with new one
                yield prisma_1.default.refreshToken.delete({
                    where: { token: refreshToken }
                });
                const newExpiresAt = new Date();
                newExpiresAt.setDate(newExpiresAt.getDate() + 7); // 7 days from now
                yield prisma_1.default.refreshToken.create({
                    data: {
                        token: newRefreshToken,
                        userId: decoded.userId,
                        expiresAt: newExpiresAt
                    }
                });
                reply.send({
                    tokens: {
                        accessToken: newAuthToken,
                        refreshToken: newRefreshToken
                    }
                });
            }
            catch (error) {
                console.error('Token refresh error:', error);
                reply.code(401).send({ error: 'Invalid refresh token' });
            }
        }));
        // Logout endpoint
        fastify.post('/logout', {
            schema: auth_2.logoutSchema
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { refreshToken } = request.body;
            try {
                // Remove refresh token from database
                // Use deleteMany to avoid throwing an error if the token is not found
                yield prisma_1.default.refreshToken.deleteMany({
                    where: { token: refreshToken }
                });
                reply.code(200).send({ message: 'Logged out successfully' });
            }
            catch (error) {
                console.error('Logout error:', error);
                reply.code(500).send({ error: 'Internal server error' });
            }
        }));
    });
}
//# sourceMappingURL=auth.js.map