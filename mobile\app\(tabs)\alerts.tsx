import { StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AlertCard } from '@/components/AlertCard';
import { StatusBadge } from '@/components/StatusBadge';
import { useAlertsStore } from '@/store/alerts-store';

export default function AlertsScreen() {
  const { alerts, markAsRead } = useAlertsStore();
  const unreadCount = alerts.filter(alert => !alert.read).length;

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Alerts</ThemedText>
        <ThemedView style={styles.headerRow}>
          <ThemedText type="subtitle">Stay updated with crypto news</ThemedText>
          {unreadCount > 0 && (
            <StatusBadge 
              text={`${unreadCount} new`} 
              variant="warning" 
            />
          )}
        </ThemedView>
      </ThemedView>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <ThemedView style={styles.content}>
          {alerts.map((alert) => (
            <AlertCard 
              key={alert.id} 
              alert={alert} 
              onPress={() => markAsRead(alert.id)}
            />
          ))}
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 0,
    gap: 12,
  },
});
