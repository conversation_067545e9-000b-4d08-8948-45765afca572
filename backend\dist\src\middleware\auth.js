"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAuth = exports.createAuthMiddleware = void 0;
const prisma_1 = __importDefault(require("../lib/prisma"));
const createAuthMiddleware = (fastify) => {
    return (request, reply) => __awaiter(void 0, void 0, void 0, function* () {
        var _a;
        try {
            const token = (_a = request.headers.authorization) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', '');
            if (!token) {
                reply.code(401).send({ error: 'No token provided' });
                return;
            }
            const decoded = fastify.jwt.verify(token);
            // Fetch user with role
            const user = yield prisma_1.default.user.findUnique({
                where: { id: decoded.userId },
                select: { id: true, role: true }
            });
            if (!user) {
                reply.code(401).send({ error: 'User not found' });
                return;
            }
            request.user = user;
        }
        catch (error) {
            reply.code(401).send({ error: 'Invalid token' });
        }
    });
};
exports.createAuthMiddleware = createAuthMiddleware;
// Helper function to protect routes
const requireAuth = (request, reply) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = yield request.jwtVerify();
        // Fetch user with role
        const user = yield prisma_1.default.user.findUnique({
            where: { id: token.userId },
            select: { id: true, role: true }
        });
        if (!user) {
            reply.code(401).send({ error: 'User not found' });
            return;
        }
        request.user = user;
    }
    catch (err) {
        reply.code(401).send({ error: 'Unauthorized' });
    }
});
exports.requireAuth = requireAuth;
//# sourceMappingURL=auth.js.map