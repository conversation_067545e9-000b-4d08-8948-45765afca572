{"version": 3, "file": "loginSecurity.js", "sourceRoot": "", "sources": ["../../../src/lib/loginSecurity.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAaA,gDASC;AAED,gDA0CC;AAED,gDASC;AA7ED,sDAA8B;AAG9B,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAC9B,MAAM,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,6BAA6B;AAStE,SAAsB,kBAAkB,CAAC,IAAsB;;QAC7D,OAAO,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE;gBACJ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB;SACF,CAAC,CAAC;IACL,CAAC;CAAA;AAED,SAAsB,kBAAkB,CAAC,KAAa,EAAE,SAAiB,EAAE,KAAmB;;QAC5F,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,CAAC;QAE3D,iEAAiE;QACjE,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpD,gBAAM,CAAC,YAAY,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE;oBACL,KAAK;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE;wBACT,GAAG,EAAE,UAAU;qBAChB;iBACF;aACF,CAAC;YACF,gBAAM,CAAC,YAAY,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE;oBACL,SAAS;oBACT,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE;wBACT,GAAG,EAAE,UAAU;qBAChB;iBACF;aACF,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,aAAa,IAAI,mBAAmB,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,uDAAuD,WAAW,WAAW;aACrF,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,UAAU,IAAI,mBAAmB,GAAG,CAAC,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,oEAAoE,WAAW,WAAW;aAClG,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CAAA;AAED,SAAsB,kBAAkB;;QACtC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC/D,MAAM,gBAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACnC,KAAK,EAAE;gBACL,SAAS,EAAE;oBACT,EAAE,EAAE,UAAU;iBACf;aACF;SACF,CAAC,CAAC;IACL,CAAC;CAAA"}