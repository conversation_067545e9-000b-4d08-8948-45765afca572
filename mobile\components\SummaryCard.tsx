import { StyleSheet } from 'react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface SummaryCardProps {
  title: string;
  value: string;
  change: number;
}

export function SummaryCard({ title, value, change }: SummaryCardProps) {
  const isPositive = change >= 0;
  const changeColor = isPositive ? '#34C759' : '#FF3B30';
  const changePrefix = isPositive ? '+' : '';

  return (
    <ThemedView style={styles.card}>
      <ThemedText type="subtitle" style={styles.title}>
        {title}
      </ThemedText>
      
      <ThemedText type="title" style={styles.value}>
        {value}
      </ThemedText>
      
      <ThemedView style={styles.changeContainer}>
        <ThemedText style={[styles.change, { color: changeColor }]}>
          {changePrefix}{change.toFixed(2)}%
        </ThemedText>
        <ThemedText style={styles.period}>24h</ThemedText>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 20,
    borderRadius: 16,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  title: {
    marginBottom: 8,
    color: '#666',
  },
  value: {
    marginBottom: 8,
    fontSize: 32,
    fontWeight: 'bold',
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  change: {
    fontSize: 16,
    fontWeight: '600',
  },
  period: {
    fontSize: 14,
    color: '#999',
  },
});
