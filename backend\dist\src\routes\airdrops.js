"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const prisma_1 = __importDefault(require("../lib/prisma"));
const auth_1 = require("../middleware/auth");
function default_1(fastify) {
    return __awaiter(this, void 0, void 0, function* () {
        // Get all airdrops for the authenticated user with filters
        fastify.get('/', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            const { status, upcoming, completed } = request.query;
            try {
                let whereClause = { userId };
                if (status) {
                    whereClause.status = status;
                }
                if (upcoming === 'true') {
                    whereClause.airdrop = {
                        deadline: {
                            gt: new Date(),
                        },
                    };
                }
                if (completed === 'true') {
                    whereClause.status = 'claimed'; // Assuming 'claimed' means completed
                }
                const userAirdrops = yield prisma_1.default.userAirdrop.findMany({
                    where: whereClause,
                    include: {
                        airdrop: true,
                    },
                });
                return { airdrops: userAirdrops };
            }
            catch (error) {
                console.error('Error fetching user airdrops:', error);
                reply.code(500).send({ error: 'Failed to fetch user airdrops' });
            }
        }));
        // Get user airdrop by ID
        fastify.get('/:id', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                const userAirdrop = yield prisma_1.default.userAirdrop.findUnique({
                    where: {
                        userId_airdropId: {
                            userId: userId,
                            airdropId: id,
                        },
                    },
                    include: {
                        airdrop: true,
                    },
                });
                if (!userAirdrop) {
                    reply.code(404).send({ error: 'User airdrop not found' });
                    return;
                }
                return { airdrop: userAirdrop };
            }
            catch (error) {
                console.error('Error fetching user airdrop:', error);
                reply.code(500).send({ error: 'Failed to fetch user airdrop' });
            }
        }));
        // Toggle airdrop claim status for authenticated user
        fastify.post('/:id/claim', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                const userAirdrop = yield prisma_1.default.userAirdrop.findUnique({
                    where: {
                        userId_airdropId: {
                            userId: userId,
                            airdropId: id,
                        },
                    },
                });
                if (!userAirdrop) {
                    reply.code(404).send({ error: 'User airdrop not found' });
                    return;
                }
                const newStatus = userAirdrop.status === 'claimed' ? 'eligible' : 'claimed';
                const updatedAirdrop = yield prisma_1.default.userAirdrop.update({
                    where: {
                        userId_airdropId: {
                            userId: userId,
                            airdropId: id,
                        },
                    },
                    data: {
                        status: newStatus,
                        claimedAt: newStatus === 'claimed' ? new Date() : null,
                    },
                    include: {
                        airdrop: true,
                    },
                });
                return { airdrop: updatedAirdrop };
            }
            catch (error) {
                console.error('Error toggling airdrop claim status:', error);
                reply.code(500).send({ error: 'Failed to toggle airdrop claim status' });
            }
        }));
        // Create airdrop
        fastify.post('/', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { title, description, criteria, deadline } = request.body;
            try {
                const airdrop = yield prisma_1.default.airdrop.create({
                    data: {
                        title,
                        description,
                        criteria,
                        deadline: new Date(deadline)
                    }
                });
                reply.code(201).send({ airdrop });
            }
            catch (error) {
                console.error('Error creating airdrop:', error);
                throw new Error('Failed to create airdrop');
            }
        }));
        // Update airdrop
        fastify.put('/:id', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const { title, description, criteria, deadline } = request.body;
            try {
                const airdrop = yield prisma_1.default.airdrop.update({
                    where: { id },
                    data: Object.assign(Object.assign(Object.assign(Object.assign({}, (title && { title })), (description !== undefined && { description })), (criteria && { criteria })), (deadline && { deadline: new Date(deadline) }))
                });
                return { airdrop };
            }
            catch (error) {
                console.error('Error updating airdrop:', error);
                throw new Error('Failed to update airdrop');
            }
        }));
        // Delete airdrop
        fastify.delete('/:id', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                yield prisma_1.default.airdrop.delete({
                    where: { id }
                });
                reply.code(204).send();
            }
            catch (error) {
                console.error('Error deleting airdrop:', error);
                throw new Error('Failed to delete airdrop');
            }
        }));
        // Get users eligible for an airdrop
        fastify.get('/:id/users', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            try {
                const userAirdrops = yield prisma_1.default.userAirdrop.findMany({
                    where: { airdropId: id },
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true
                            }
                        }
                    }
                });
                return { users: userAirdrops };
            }
            catch (error) {
                console.error('Error fetching airdrop users:', error);
                throw new Error('Failed to fetch airdrop users');
            }
        }));
        // Add user to airdrop
        fastify.post('/:id/users', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const { userId, status } = request.body;
            try {
                const userAirdrop = yield prisma_1.default.userAirdrop.create({
                    data: {
                        userId,
                        airdropId: id,
                        status
                    },
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true
                            }
                        },
                        airdrop: true
                    }
                });
                reply.code(201).send({ userAirdrop });
            }
            catch (error) {
                console.error('Error adding user to airdrop:', error);
                throw new Error('Failed to add user to airdrop');
            }
        }));
        // Update user airdrop status
        fastify.put('/:id/users/:userId', (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id, userId } = request.params;
            const { status } = request.body;
            try {
                const userAirdrop = yield prisma_1.default.userAirdrop.update({
                    where: {
                        userId_airdropId: {
                            userId,
                            airdropId: id
                        }
                    },
                    data: Object.assign({ status }, (status === 'claimed' && { claimedAt: new Date() })),
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true
                            }
                        },
                        airdrop: true
                    }
                });
                return { userAirdrop };
            }
            catch (error) {
                console.error('Error updating user airdrop status:', error);
                throw new Error('Failed to update user airdrop status');
            }
        }));
    });
}
//# sourceMappingURL=airdrops.js.map