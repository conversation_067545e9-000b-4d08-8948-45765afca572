"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../src/generated/prisma");
const bcrypt = __importStar(require("bcrypt"));
const prisma = new prisma_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        // Clear existing data
        yield prisma.transaction.deleteMany();
        yield prisma.portfolioHolding.deleteMany();
        yield prisma.userAirdrop.deleteMany();
        yield prisma.alert.deleteMany();
        yield prisma.airdrop.deleteMany();
        yield prisma.user.deleteMany();
        console.log('Seeding database...');
        // Create users
        const passwordHash = yield bcrypt.hash('password123', 10);
        const user1 = yield prisma.user.create({
            data: {
                email: '<EMAIL>',
                passwordHash,
            },
        });
        const user2 = yield prisma.user.create({
            data: {
                email: '<EMAIL>',
                passwordHash,
            },
        });
        console.log('Created users:', { user1, user2 });
        // Create airdrops
        const airdrop1 = yield prisma.airdrop.create({
            data: {
                title: 'Ethereum 2.0 Staking Rewards',
                description: 'Rewards for early Ethereum 2.0 stakers',
                criteria: 'Must have staked at least 32 ETH before the Merge',
                deadline: new Date('2025-12-31'),
            },
        });
        const airdrop2 = yield prisma.airdrop.create({
            data: {
                title: 'Uniswap V4 Launch',
                description: 'Airdrop for Uniswap V3 users',
                criteria: 'Must have provided liquidity on Uniswap V3',
                deadline: new Date('2025-10-15'),
            },
        });
        console.log('Created airdrops:', { airdrop1, airdrop2 });
        // Create user-airdrop relationships
        const userAirdrop1 = yield prisma.userAirdrop.create({
            data: {
                userId: user1.id,
                airdropId: airdrop1.id,
                status: 'eligible',
            },
        });
        const userAirdrop2 = yield prisma.userAirdrop.create({
            data: {
                userId: user2.id,
                airdropId: airdrop2.id,
                status: 'claimed',
                claimedAt: new Date(),
            },
        });
        console.log('Created user-airdrop relationships:', { userAirdrop1, userAirdrop2 });
        // Create portfolio holdings
        const holding1 = yield prisma.portfolioHolding.create({
            data: {
                userId: user1.id,
                tokenSymbol: 'ETH',
                tokenName: 'Ethereum',
                currentAmount: 2.5,
            },
        });
        const holding2 = yield prisma.portfolioHolding.create({
            data: {
                userId: user1.id,
                tokenSymbol: 'BTC',
                tokenName: 'Bitcoin',
                currentAmount: 0.1,
            },
        });
        const holding3 = yield prisma.portfolioHolding.create({
            data: {
                userId: user2.id,
                tokenSymbol: 'SOL',
                tokenName: 'Solana',
                currentAmount: 50,
            },
        });
        console.log('Created portfolio holdings:', { holding1, holding2, holding3 });
        // Create transactions
        const transaction1 = yield prisma.transaction.create({
            data: {
                holdingId: holding1.id,
                type: 'BUY',
                amount: 1.5,
                pricePerToken: 3000,
                totalValue: 4500,
                date: new Date('2025-01-15'),
            },
        });
        const transaction2 = yield prisma.transaction.create({
            data: {
                holdingId: holding1.id,
                type: 'BUY',
                amount: 1.0,
                pricePerToken: 3200,
                totalValue: 3200,
                date: new Date('2025-02-20'),
            },
        });
        const transaction3 = yield prisma.transaction.create({
            data: {
                holdingId: holding2.id,
                type: 'BUY',
                amount: 0.1,
                pricePerToken: 50000,
                totalValue: 5000,
                date: new Date('2025-03-10'),
            },
        });
        console.log('Created transactions:', { transaction1, transaction2, transaction3 });
        // Create alerts
        const alert1 = yield prisma.alert.create({
            data: {
                userId: user1.id,
                type: 'price',
                condition: 'above',
                threshold: 4000,
                tokenSymbol: 'ETH',
                active: true,
            },
        });
        const alert2 = yield prisma.alert.create({
            data: {
                userId: user2.id,
                type: 'airdrop',
                condition: 'deadline',
                airdropId: airdrop2.id,
                active: true,
            },
        });
        console.log('Created alerts:', { alert1, alert2 });
        console.log('Database seeding completed!');
    });
}
main()
    .catch((e) => {
    console.error('Error seeding database:', e);
    process.exit(1);
})
    .finally(() => __awaiter(void 0, void 0, void 0, function* () {
    yield prisma.$disconnect();
}));
//# sourceMappingURL=seed.js.map