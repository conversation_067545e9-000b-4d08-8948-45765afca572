{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,2DAAmC;AA4B5B,MAAM,oBAAoB,GAAG,CAAC,OAAwB,EAAE,EAAE;IAC/D,OAAO,CAAO,OAAuB,EAAE,KAAmB,EAAE,EAAE;;QAC5D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,aAAa,0CAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEpE,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAa,KAAK,CAAC,CAAC;YAEtD,uBAAuB;YACvB,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;gBAC7B,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;aACjC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;gBAClD,OAAO;YACT,CAAC;YAED,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC,CAAA,CAAC;AACJ,CAAC,CAAC;AA7BW,QAAA,oBAAoB,wBA6B/B;AAEF,oCAAoC;AAC7B,MAAM,WAAW,GAAG,CAAO,OAAuB,EAAE,KAAmB,EAAE,EAAE;IAChF,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,SAAS,EAAc,CAAC;QAEpD,uBAAuB;QACvB,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE;YAC3B,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAClD,OAAO;QACT,CAAC;QAED,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACtB,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAA,CAAC;AAnBW,QAAA,WAAW,eAmBtB"}