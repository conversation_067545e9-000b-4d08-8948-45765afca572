import { create } from 'zustand';
import type { Airdrop } from '@/types/crypto';

interface AirdropsState {
  airdrops: Airdrop[];
  loading: boolean;
  error: string | null;
  getAirdropById: (id: string) => Airdrop | undefined;
  refreshAirdrops: () => Promise<void>;
  addAirdrop: (airdrop: Airdrop) => void;
  updateAirdrop: (id: string, updates: Partial<Airdrop>) => void;
  removeAirdrop: (id: string) => void;
}

// Mock data for development
const mockAirdrops: Airdrop[] = [
  {
    id: '1',
    name: 'LayerZero',
    symbol: 'ZRO',
    description: 'Omnichain interoperability protocol enabling seamless cross-chain transactions.',
    logo: 'https://cryptologos.cc/logos/layerzero-zro-logo.png',
    status: 'Active',
    endDate: '2024-03-15',
    estimatedValue: '$500-2000',
    requirements: 'Bridge transactions across multiple chains',
    website: 'https://layerzero.network',
  },
  {
    id: '2',
    name: 'zkSync Era',
    symbol: 'ZK',
    description: 'Layer 2 scaling solution for Ethereum using zero-knowledge proofs.',
    logo: 'https://cryptologos.cc/logos/zksync-zk-logo.png',
    status: 'Upcoming',
    endDate: '2024-04-01',
    estimatedValue: '$1000-5000',
    requirements: 'Use zkSync Era for transactions and DeFi',
    website: 'https://zksync.io',
  },
  {
    id: '3',
    name: 'Starknet',
    symbol: 'STRK',
    description: 'Decentralized ZK-Rollup operating as an L2 network over Ethereum.',
    logo: 'https://cryptologos.cc/logos/starknet-strk-logo.png',
    status: 'Active',
    endDate: '2024-02-28',
    estimatedValue: '$200-1000',
    requirements: 'Deploy contracts and make transactions',
    website: 'https://starknet.io',
  },
];

export const useAirdropsStore = create<AirdropsState>((set, get) => ({
  airdrops: mockAirdrops,
  loading: false,
  error: null,

  getAirdropById: (id: string) => {
    return get().airdrops.find(airdrop => airdrop.id === id);
  },

  refreshAirdrops: async () => {
    set({ loading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // In a real app, you would fetch from an API here
      set({ loading: false });
    } catch (error) {
      set({ 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to refresh airdrops' 
      });
    }
  },

  addAirdrop: (airdrop: Airdrop) => {
    set(state => ({
      airdrops: [...state.airdrops, airdrop]
    }));
  },

  updateAirdrop: (id: string, updates: Partial<Airdrop>) => {
    set(state => ({
      airdrops: state.airdrops.map(airdrop =>
        airdrop.id === id ? { ...airdrop, ...updates } : airdrop
      )
    }));
  },

  removeAirdrop: (id: string) => {
    set(state => ({
      airdrops: state.airdrops.filter(airdrop => airdrop.id !== id)
    }));
  },
}));
