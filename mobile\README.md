# Nobify Crypto Manager Mobile App 📱

A React Native mobile application built with Expo for managing cryptocurrency airdrops, portfolio tracking, and market alerts.

## Features

- 🎁 **Airdrop Tracking**: Discover and track upcoming cryptocurrency airdrops
- 📊 **Portfolio Management**: Monitor your crypto holdings and performance
- 🔔 **Smart Alerts**: Get notified about important market events and deadlines
- 📈 **Dashboard**: Overview of your crypto investments and opportunities
- 🌙 **Dark/Light Mode**: Automatic theme switching based on system preferences

## Tech Stack

- **Framework**: React Native with Expo
- **Routing**: Expo Router (file-based routing)
- **State Management**: Zustand
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **UI Components**: Custom themed components
- **Icons**: Expo Symbols
- **Images**: Expo Image
- **TypeScript**: Full type safety

## Project Structure

```
mobile/
├── app/                    # App screens (file-based routing)
│   ├── (tabs)/            # Tab navigation screens
│   │   ├── index.tsx      # Airdrops screen
│   │   ├── airdrops.tsx   # Airdrops screen (alternative)
│   │   ├── alerts.tsx     # Alerts screen
│   │   ├── dashboard.tsx  # Dashboard screen
│   │   └── portfolio.tsx  # Portfolio screen
│   ├── airdrop-details/   # Airdrop detail screens
│   │   ├── [id].tsx       # Dynamic airdrop details
│   │   ├── alert-form.tsx # Alert creation form
│   │   └── modal.tsx      # Modal screen
│   ├── _layout.tsx        # Root layout
│   └── +not-found.tsx     # 404 screen
├── assets/                # Static assets
│   ├── images/           # App images and icons
│   └── fonts/            # Custom fonts
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components
│   ├── AirdropCard.tsx  # Airdrop display card
│   ├── AlertCard.tsx    # Alert display card
│   ├── StatusBadge.tsx  # Status indicator
│   ├── SummaryCard.tsx  # Summary display card
│   ├── TokenCard.tsx    # Token display card
│   ├── ThemedText.tsx   # Themed text component
│   └── ThemedView.tsx   # Themed view component
├── constants/           # App constants
│   └── Colors.ts        # Color definitions
├── hooks/              # Custom React hooks
│   ├── useColorScheme.ts
│   └── useThemeColor.ts
├── store/              # Zustand state stores
│   ├── airdrops-store.ts
│   ├── alerts-store.ts
│   └── portfolio-store.ts
└── types/              # TypeScript type definitions
    └── crypto.ts       # Crypto-related types
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Bun (package manager)
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Install dependencies**
   ```bash
   bun install
   ```

2. **Start the development server**
   ```bash
   bun start
   ```

3. **Run on specific platforms**
   ```bash
   # iOS Simulator
   bun run ios

   # Android Emulator
   bun run android

   # Web browser
   bun run web
   ```

### Development Scripts

- `bun start` - Start the Expo development server
- `bun start-web` - Start with web support
- `bun start-web-dev` - Start with debug mode for web

## Key Components

### State Management

The app uses Zustand for state management with three main stores:

- **AirdropsStore**: Manages airdrop data and operations
- **AlertsStore**: Handles notifications and alerts
- **PortfolioStore**: Tracks user's crypto portfolio

### Navigation

Built with Expo Router using file-based routing:
- Tab navigation for main screens
- Stack navigation for detailed views
- Modal presentations for forms and details

### Theming

Supports both light and dark themes with:
- Automatic system theme detection
- Custom color schemes
- Themed components for consistent styling

## API Integration

The app is designed to integrate with crypto APIs for:
- Real-time price data
- Airdrop information
- Market alerts
- Portfolio tracking

Currently uses mock data for development.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is part of the Nobify Crypto Manager suite.