"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const prisma_1 = __importDefault(require("../lib/prisma"));
const auth_1 = require("../middleware/auth");
const rbac_1 = require("../middleware/rbac");
const prisma_2 = require("../generated/prisma");
function default_1(fastify) {
    return __awaiter(this, void 0, void 0, function* () {
        // Get all users (admin only)
        fastify.get('/', {
            preHandler: [auth_1.requireAuth, (0, rbac_1.requireRole)(prisma_2.UserRole.ADMIN)]
        }, (request) => __awaiter(this, void 0, void 0, function* () {
            try {
                const users = yield prisma_1.default.user.findMany({
                    select: {
                        id: true,
                        email: true,
                        createdAt: true,
                        updatedAt: true
                    }
                });
                return { users };
            }
            catch (error) {
                console.error('Error fetching users:', error);
                throw new Error('Failed to fetch users');
            }
        }));
        // Get user by ID (admin or self)
        fastify.get('/:id', {
            preHandler: [auth_1.requireAuth],
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const currentUser = request.user;
            // Allow access if user is admin or accessing their own profile
            if (currentUser.role !== prisma_2.UserRole.ADMIN && currentUser.id !== id) {
                reply.code(403).send({ error: 'Forbidden: Cannot view other users' });
                return;
            }
            try {
                const user = yield prisma_1.default.user.findUnique({
                    where: { id },
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        profileImage: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    }
                });
                if (!user) {
                    reply.code(404).send({ error: 'User not found' });
                    return;
                }
                return { user };
            }
            catch (error) {
                console.error('Error fetching user:', error);
                throw new Error('Failed to fetch user');
            }
        }));
        // Update user (protected route)
        fastify.put('/:id', {
            preHandler: [auth_1.requireAuth]
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            // Ensure users can only update their own profile
            if (request.user.id !== id) {
                reply.code(403).send({ error: 'Forbidden: Cannot update other users' });
                return;
            }
            const { email } = request.body;
            try {
                const user = yield prisma_1.default.user.update({
                    where: { id },
                    data: { email },
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        profileImage: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    }
                });
                return { user };
            }
            catch (error) {
                console.error('Error updating user:', error);
                throw new Error('Failed to update user');
            }
        }));
        // Delete user (protected route)
        fastify.delete('/:id', {
            preHandler: [auth_1.requireAuth]
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            // Ensure users can only delete their own profile
            if (request.user.id !== id) {
                reply.code(403).send({ error: 'Forbidden: Cannot delete other users' });
                return;
            }
            try {
                yield prisma_1.default.user.delete({
                    where: { id }
                });
                reply.code(204).send();
            }
            catch (error) {
                console.error('Error deleting user:', error);
                throw new Error('Failed to delete user');
            }
        }));
    });
}
//# sourceMappingURL=users.js.map