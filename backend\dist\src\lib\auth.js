"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hashPassword = hashPassword;
exports.verifyPassword = verifyPassword;
exports.generateAuthToken = generateAuthToken;
exports.generateRefreshToken = generateRefreshToken;
const bcrypt_1 = __importDefault(require("bcrypt"));
const SALT_ROUNDS = 10;
function hashPassword(password) {
    return __awaiter(this, void 0, void 0, function* () {
        return bcrypt_1.default.hash(password, SALT_ROUNDS);
    });
}
function verifyPassword(password, hash) {
    return __awaiter(this, void 0, void 0, function* () {
        return bcrypt_1.default.compare(password, hash);
    });
}
function generateAuthToken(fastify, userId) {
    return fastify.jwt.sign({ userId }, {
        expiresIn: '1h' // Token expires in 1 hour
    });
}
function generateRefreshToken(fastify, userId) {
    return fastify.jwt.sign({ userId, type: 'refresh' }, {
        expiresIn: '7d' // Refresh token expires in 7 days
    });
}
//# sourceMappingURL=auth.js.map