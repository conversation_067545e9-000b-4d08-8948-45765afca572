{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/routes/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAMA,4BAyOC;AA9OD,sCAAoG;AACpG,wDAAkG;AAClG,2DAAmC;AACnC,0CAA8F;AAE9F,mBAA+B,OAAwB;;QACrD,kBAAkB;QAClB,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;YACtB,MAAM,EAAE,mBAAY;SACrB,EAAE,CAAO,OAAsE,EAAE,KAAmB,EAAE,EAAE;YACvG,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAEzC,IAAI,CAAC;gBACH,+BAA+B;gBAC/B,MAAM,YAAY,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAChD,KAAK,EAAE,EAAE,KAAK,EAAE;iBACjB,CAAC,CAAC;gBAEH,IAAI,YAAY,EAAE,CAAC;oBACjB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;oBAC5D,OAAO;gBACT,CAAC;gBAED,uDAAuD;gBACvD,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAY,EAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACpC,IAAI,EAAE;wBACJ,KAAK;wBACL,YAAY,EAAE,cAAc;wBAC5B,IAAI,EAAE,MAAM;qBACb;oBACD,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,IAAI;qBAChB;iBACF,CAAC,CAAC;gBAEH,kBAAkB;gBAClB,MAAM,SAAS,GAAG,IAAA,wBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtD,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE5D,kDAAkD;gBAClD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;gBAE9D,MAAM,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,IAAI,EAAE;wBACJ,KAAK,EAAE,YAAY;wBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,SAAS;qBACV;iBACF,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,IAAI;oBACJ,MAAM,EAAE;wBACN,WAAW,EAAE,SAAS;wBACtB,YAAY;qBACb;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAA,CAAC,CAAC;QAEH,iBAAiB;QACjB,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;YACrB,MAAM,EAAE,kBAAW;SACpB,EAAE,CAAO,OAAsE,EAAE,KAAmB,EAAE,EAAE;YACvG,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAEzC,IAAI,CAAC;gBACH,8BAA8B;gBAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;gBAE7B,qCAAqC;gBACrC,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAkB,EAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBACrE,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO;gBACT,CAAC;gBAED,YAAY;gBACZ,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,KAAK,EAAE,EAAE,KAAK,EAAE;iBACjB,CAAC,CAAC;gBAEH,2CAA2C;gBAC3C,MAAM,WAAW,GAAG;oBAClB,KAAK;oBACL,SAAS;oBACT,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE;iBACjB,CAAC;gBAEF,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,IAAA,kCAAkB,EAAC,WAAW,CAAC,CAAC;oBACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBACvD,OAAO;gBACT,CAAC;gBAED,kBAAkB;gBAClB,MAAM,OAAO,GAAG,MAAM,IAAA,qBAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAClE,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAA,kCAAkB,EAAC,WAAW,CAAC,CAAC;oBACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBACvD,OAAO;gBACT,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,IAAA,kCAAkB,kCACnB,WAAW,KACd,OAAO,EAAE,IAAI,IACb,CAAC;gBAEH,iDAAiD;gBACjD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;oBACzB,IAAA,kCAAkB,GAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5C,CAAC;gBAED,kBAAkB;gBAClB,MAAM,SAAS,GAAG,IAAA,wBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtD,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE5D,kDAAkD;gBAClD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;gBAE9D,MAAM,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,IAAI,EAAE;wBACJ,KAAK,EAAE,YAAY;wBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,SAAS;qBACV;iBACF,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC;oBACT,IAAI,EAAE;wBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,SAAS,EAAE,IAAI,CAAC,SAAS;qBAC1B;oBACD,MAAM,EAAE;wBACN,WAAW,EAAE,SAAS;wBACtB,YAAY;qBACb;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACrC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAA,CAAC,CAAC;QAEH,yBAAyB;QACzB,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC7B,MAAM,EAAE,yBAAkB;SAC3B,EAAE,CAAO,OAA2D,EAAE,KAAmB,EAAE,EAAE;YAC5F,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAEtC,IAAI,CAAC;gBACH,uBAAuB;gBACvB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAmC,YAAY,CAAC,CAAC;gBAEnF,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC/B,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;oBACzD,OAAO;gBACT,CAAC;gBAED,mDAAmD;gBACnD,MAAM,WAAW,GAAG,MAAM,gBAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oBACvD,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;iBAC/B,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;oBACvD,oCAAoC;oBACpC,IAAI,WAAW,EAAE,CAAC;wBAChB,MAAM,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;4BAC/B,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;yBAC/B,CAAC,CAAC;oBACL,CAAC;oBACD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC,CAAC;oBACpE,OAAO;gBACT,CAAC;gBAED,sBAAsB;gBACtB,MAAM,YAAY,GAAG,IAAA,wBAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,eAAe,GAAG,IAAA,2BAAoB,EAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAEtE,yCAAyC;gBACzC,MAAM,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;iBAC/B,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;gBAChC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;gBAEpE,MAAM,gBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,IAAI,EAAE;wBACJ,KAAK,EAAE,eAAe;wBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,SAAS,EAAE,YAAY;qBACxB;iBACF,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC;oBACT,MAAM,EAAE;wBACN,WAAW,EAAE,YAAY;wBACzB,YAAY,EAAE,eAAe;qBAC9B;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBAC7C,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAA,CAAC,CAAC;QAEH,kBAAkB;QAClB,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;YACtB,MAAM,EAAE,mBAAY;SACrB,EAAE,CAAO,OAA2D,EAAE,KAAmB,EAAE,EAAE;YAC5F,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAEtC,IAAI,CAAC;gBACH,qCAAqC;gBACrC,sEAAsE;gBACtE,MAAM,gBAAM,CAAC,YAAY,CAAC,UAAU,CAAC;oBACnC,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;iBAC/B,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;YAC/D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBACtC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAA,CAAC,CAAC;IACL,CAAC;CAAA"}