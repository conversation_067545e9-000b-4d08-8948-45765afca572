export interface Airdrop {
  id: string;
  name: string;
  symbol: string;
  description: string;
  logo: string;
  status: 'Active' | 'Upcoming' | 'Ended' | 'Claimed';
  endDate: string;
  estimatedValue: string;
  requirements: string;
  website?: string;
  twitterUrl?: string;
  discordUrl?: string;
  telegramUrl?: string;
}

export interface Token {
  id: string;
  name: string;
  symbol: string;
  logo: string;
  value: number; // Current price in USD
  change24h: number; // 24h change percentage
  amount: number; // Amount owned
}

export interface Alert {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  type: 'airdrop' | 'market' | 'portfolio' | 'general';
  airdropId?: string; // Reference to airdrop if type is 'airdrop'
  tokenId?: string; // Reference to token if type is 'portfolio'
}

export interface Portfolio {
  tokens: Token[];
  totalValue: number;
  totalChange24h: number;
  lastUpdated: string;
}

export interface MarketData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: string;
}

export interface User {
  id: string;
  email: string;
  username: string;
  walletAddresses: WalletAddress[];
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface WalletAddress {
  id: string;
  address: string;
  blockchain: 'ethereum' | 'solana' | 'polygon' | 'arbitrum' | 'optimism' | 'bsc';
  label?: string;
  isActive: boolean;
}

export interface UserPreferences {
  notifications: {
    airdropDeadlines: boolean;
    priceAlerts: boolean;
    portfolioUpdates: boolean;
    marketNews: boolean;
  };
  theme: 'light' | 'dark' | 'auto';
  currency: 'USD' | 'EUR' | 'GBP' | 'JPY';
  language: string;
}
