# App Assets

This directory contains the app's visual assets.

## Required Files

### App Icons
- `icon.png` - Main app icon (1024x1024)
- `adaptive-icon.png` - Android adaptive icon foreground (1024x1024)
- `favicon.png` - Web favicon (32x32 or 48x48)

### Splash Screen
- `splash-icon.png` - Splash screen icon (400x400 recommended)

## Icon Generation Tools

1. **App Icon Generator**: https://www.appicon.co/
2. **Icon Kitchen**: https://icon.kitchen/
3. **Figma**: Design custom icons
4. **Canva**: Quick icon creation

## Design Guidelines

### App Icon
- Size: 1024x1024 pixels
- Format: PNG with transparency
- Style: Simple, recognizable, scalable
- Colors: Match your brand colors

### Adaptive Icon (Android)
- Size: 1024x1024 pixels
- Safe area: 66% of the icon (center 672x672)
- Background: Solid color or simple pattern
- Foreground: Main icon element

### Splash Screen
- Keep it simple and fast-loading
- Use your brand colors
- Center the logo/icon
- Avoid text (except brand name)

## Current Status

🔴 **Missing**: All icon files need to be added
📝 **TODO**: 
- [ ] Create app icon design
- [ ] Generate all required sizes
- [ ] Add splash screen image
- [ ] Test on different devices
