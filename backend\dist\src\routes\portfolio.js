"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const prisma_1 = __importDefault(require("../lib/prisma"));
const auth_1 = require("../middleware/auth");
const portfolioService_1 = require("../services/portfolioService");
const cryptoApi_1 = require("../lib/cryptoApi");
// Validation schemas
const createHoldingSchema = {
    type: 'object',
    required: ['tokenSymbol', 'tokenName', 'currentAmount'],
    properties: {
        tokenSymbol: { type: 'string', minLength: 1, maxLength: 10 },
        tokenName: { type: 'string', minLength: 1, maxLength: 100 },
        currentAmount: { type: 'number', minimum: 0 }
    }
};
const updateHoldingSchema = {
    type: 'object',
    required: ['currentAmount'],
    properties: {
        currentAmount: { type: 'number', minimum: 0 }
    }
};
const createTransactionSchema = {
    type: 'object',
    required: ['holdingId', 'type', 'amount', 'date'],
    properties: {
        holdingId: { type: 'string' },
        type: { type: 'string', enum: ['BUY', 'SELL', 'TRANSFER_IN', 'TRANSFER_OUT', 'STAKE', 'UNSTAKE', 'REWARD', 'AIRDROP'] },
        amount: { type: 'number', minimum: 0 },
        pricePerToken: { type: 'number', minimum: 0 },
        totalValue: { type: 'number', minimum: 0 },
        transactionFee: { type: 'number', minimum: 0 },
        feeTokenSymbol: { type: 'string', maxLength: 10 },
        exchangeName: { type: 'string', maxLength: 50 },
        transactionHash: { type: 'string', maxLength: 100 },
        date: { type: 'string', format: 'date-time' },
        notes: { type: 'string', maxLength: 500 }
    }
};
function default_1(fastify) {
    return __awaiter(this, void 0, void 0, function* () {
        // Get user's portfolio summary and holdings
        fastify.get('/', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const [summary, holdings] = yield Promise.all([
                    (0, portfolioService_1.getPortfolioSummary)(userId),
                    (0, portfolioService_1.getHoldingsWithMetrics)(userId)
                ]);
                return {
                    summary,
                    holdings
                };
            }
            catch (error) {
                console.error('Error fetching portfolio:', error);
                reply.code(500).send({ error: 'Failed to fetch portfolio' });
            }
        }));
        // Get portfolio summary only
        fastify.get('/summary', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const summary = yield (0, portfolioService_1.getPortfolioSummary)(userId);
                return { summary };
            }
            catch (error) {
                console.error('Error fetching portfolio summary:', error);
                reply.code(500).send({ error: 'Failed to fetch portfolio summary' });
            }
        }));
        // Get all holdings for authenticated user
        fastify.get('/holdings', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const holdings = yield (0, portfolioService_1.getHoldingsWithMetrics)(userId);
                return { holdings };
            }
            catch (error) {
                console.error('Error fetching holdings:', error);
                reply.code(500).send({ error: 'Failed to fetch holdings' });
            }
        }));
        // Get specific holding
        fastify.get('/holdings/:id', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                const holding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: {
                        id,
                        userId // Ensure user can only access their own holdings
                    },
                    include: {
                        transactions: {
                            orderBy: { date: 'desc' }
                        }
                    }
                });
                if (!holding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                return { holding };
            }
            catch (error) {
                console.error('Error fetching holding:', error);
                reply.code(500).send({ error: 'Failed to fetch holding' });
            }
        }));
        // Add new holding
        fastify.post('/holdings', {
            preHandler: [auth_1.requireAuth],
            schema: { body: createHoldingSchema }
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            const { tokenSymbol, tokenName, currentAmount } = request.body;
            try {
                // Normalize token symbol to uppercase
                const normalizedSymbol = tokenSymbol.toUpperCase();
                // Check if holding already exists for this user and token
                const existingHolding = yield prisma_1.default.portfolioHolding.findUnique({
                    where: {
                        userId_tokenSymbol: {
                            userId,
                            tokenSymbol: normalizedSymbol
                        }
                    }
                });
                if (existingHolding) {
                    reply.code(400).send({
                        error: 'Holding for this token already exists',
                        existingHolding: {
                            id: existingHolding.id,
                            tokenSymbol: existingHolding.tokenSymbol,
                            currentAmount: existingHolding.currentAmount
                        }
                    });
                    return;
                }
                const holding = yield prisma_1.default.portfolioHolding.create({
                    data: {
                        userId,
                        tokenSymbol: normalizedSymbol,
                        tokenName,
                        currentAmount
                    }
                });
                reply.code(201).send({
                    message: 'Holding created successfully',
                    holding
                });
            }
            catch (error) {
                console.error('Error creating holding:', error);
                reply.code(500).send({ error: 'Failed to create holding' });
            }
        }));
        // Update holding (mainly for manual adjustments)
        fastify.put('/holdings/:id', {
            preHandler: [auth_1.requireAuth],
            schema: { body: updateHoldingSchema }
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            const { currentAmount } = request.body;
            try {
                // Verify holding belongs to user
                const existingHolding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: { id, userId }
                });
                if (!existingHolding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                const holding = yield prisma_1.default.portfolioHolding.update({
                    where: { id },
                    data: {
                        currentAmount,
                        updatedAt: new Date()
                    }
                });
                // Recalculate metrics after manual update
                yield (0, portfolioService_1.calculateHoldingMetrics)(id);
                reply.code(200).send({
                    message: 'Holding updated successfully',
                    holding
                });
            }
            catch (error) {
                console.error('Error updating holding:', error);
                reply.code(500).send({ error: 'Failed to update holding' });
            }
        }));
        // Delete holding
        fastify.delete('/holdings/:id', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                // Verify holding belongs to user
                const existingHolding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: { id, userId }
                });
                if (!existingHolding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                // Use transaction to ensure data consistency
                yield prisma_1.default.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                    // First delete all transactions associated with this holding
                    yield tx.transaction.deleteMany({
                        where: { holdingId: id }
                    });
                    // Then delete the holding
                    yield tx.portfolioHolding.delete({
                        where: { id }
                    });
                }));
                reply.code(200).send({
                    message: 'Holding and all associated transactions deleted successfully'
                });
            }
            catch (error) {
                console.error('Error deleting holding:', error);
                reply.code(500).send({ error: 'Failed to delete holding' });
            }
        }));
        // Add transaction to holding
        fastify.post('/transactions', {
            preHandler: [auth_1.requireAuth],
            schema: { body: createTransactionSchema }
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            const { holdingId, type, amount, pricePerToken, totalValue, transactionFee, feeTokenSymbol, exchangeName, transactionHash, date, notes } = request.body;
            try {
                // Verify holding belongs to user
                const holding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: { id: holdingId, userId }
                });
                if (!holding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                // Calculate totalValue if not provided
                const calculatedTotalValue = totalValue || (pricePerToken && amount ? pricePerToken * amount : undefined);
                yield (0, portfolioService_1.addTransaction)(holdingId, {
                    type,
                    amount,
                    pricePerToken,
                    totalValue: calculatedTotalValue,
                    transactionFee,
                    feeTokenSymbol,
                    exchangeName,
                    transactionHash,
                    date: new Date(date),
                    notes
                });
                reply.code(201).send({
                    message: 'Transaction added successfully',
                    holdingId,
                    transactionType: type,
                    amount
                });
            }
            catch (error) {
                console.error('Error creating transaction:', error);
                reply.code(500).send({ error: 'Failed to create transaction' });
            }
        }));
        // Get transactions for a holding
        fastify.get('/holdings/:id/transactions', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                // Verify holding belongs to user
                const holding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: { id, userId }
                });
                if (!holding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                const transactions = yield prisma_1.default.transaction.findMany({
                    where: { holdingId: id },
                    orderBy: { date: 'desc' }
                });
                return {
                    transactions,
                    holdingInfo: {
                        id: holding.id,
                        tokenSymbol: holding.tokenSymbol,
                        tokenName: holding.tokenName
                    }
                };
            }
            catch (error) {
                console.error('Error fetching transactions:', error);
                reply.code(500).send({ error: 'Failed to fetch transactions' });
            }
        }));
        // Get all transactions for user
        fastify.get('/transactions', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const transactions = yield prisma_1.default.transaction.findMany({
                    where: {
                        holding: { userId }
                    },
                    include: {
                        holding: {
                            select: {
                                tokenSymbol: true,
                                tokenName: true
                            }
                        }
                    },
                    orderBy: { date: 'desc' }
                });
                return { transactions };
            }
            catch (error) {
                console.error('Error fetching transactions:', error);
                reply.code(500).send({ error: 'Failed to fetch transactions' });
            }
        }));
        // Get specific transaction
        fastify.get('/transactions/:id', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                const transaction = yield prisma_1.default.transaction.findFirst({
                    where: {
                        id,
                        holding: { userId }
                    },
                    include: {
                        holding: {
                            select: {
                                tokenSymbol: true,
                                tokenName: true
                            }
                        }
                    }
                });
                if (!transaction) {
                    reply.code(404).send({ error: 'Transaction not found' });
                    return;
                }
                return { transaction };
            }
            catch (error) {
                console.error('Error fetching transaction:', error);
                reply.code(500).send({ error: 'Failed to fetch transaction' });
            }
        }));
        // Delete transaction
        fastify.delete('/transactions/:id', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const { id } = request.params;
            const userId = request.user.id;
            try {
                // Get the transaction and verify it belongs to user
                const transaction = yield prisma_1.default.transaction.findFirst({
                    where: {
                        id,
                        holding: { userId }
                    },
                    include: { holding: true }
                });
                if (!transaction) {
                    reply.code(404).send({ error: 'Transaction not found' });
                    return;
                }
                const holdingId = transaction.holdingId;
                // Delete the transaction
                yield prisma_1.default.transaction.delete({
                    where: { id }
                });
                // Recalculate holding metrics after deletion
                yield (0, portfolioService_1.calculateHoldingMetrics)(holdingId);
                reply.code(200).send({
                    message: 'Transaction deleted successfully',
                    holdingId,
                    deletedTransactionType: transaction.type
                });
            }
            catch (error) {
                console.error('Error deleting transaction:', error);
                reply.code(500).send({ error: 'Failed to delete transaction' });
            }
        }));
        // Update portfolio prices manually
        fastify.post('/refresh-prices', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const result = yield (0, portfolioService_1.updateHoldingPrices)(userId);
                reply.code(200).send({
                    message: 'Portfolio prices updated successfully',
                    stats: result
                });
            }
            catch (error) {
                console.error('Error updating prices:', error);
                reply.code(500).send({ error: 'Failed to update prices' });
            }
        }));
        // Enhanced transaction creation with historical price lookup
        fastify.post('/transactions/with-price-lookup', {
            preHandler: [auth_1.requireAuth],
            schema: { body: createTransactionSchema }
        }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            const transactionData = request.body;
            try {
                // Verify holding belongs to user
                const holding = yield prisma_1.default.portfolioHolding.findFirst({
                    where: { id: transactionData.holdingId, userId }
                });
                if (!holding) {
                    reply.code(404).send({ error: 'Holding not found' });
                    return;
                }
                const result = yield (0, portfolioService_1.addTransactionWithPriceLookup)(transactionData.holdingId, {
                    type: transactionData.type,
                    amount: transactionData.amount,
                    pricePerToken: transactionData.pricePerToken,
                    totalValue: transactionData.totalValue,
                    transactionFee: transactionData.transactionFee,
                    feeTokenSymbol: transactionData.feeTokenSymbol,
                    exchangeName: transactionData.exchangeName,
                    transactionHash: transactionData.transactionHash,
                    date: new Date(transactionData.date),
                    notes: transactionData.notes
                });
                reply.code(201).send(result);
            }
            catch (error) {
                console.error('Error creating transaction with price lookup:', error);
                reply.code(500).send({ error: 'Failed to create transaction' });
            }
        }));
        // Get portfolio analytics
        fastify.get('/analytics', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            try {
                const analytics = yield (0, portfolioService_1.getPortfolioAnalytics)(userId);
                return { analytics };
            }
            catch (error) {
                console.error('Error fetching portfolio analytics:', error);
                reply.code(500).send({ error: 'Failed to fetch analytics' });
            }
        }));
        // Get price alerts for user's holdings
        fastify.get('/price-alerts', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            const userId = request.user.id;
            const { threshold } = request.query;
            try {
                const holdings = yield prisma_1.default.portfolioHolding.findMany({
                    where: { userId },
                    select: { tokenSymbol: true }
                });
                const tokenSymbols = holdings.map(h => h.tokenSymbol);
                const thresholdPercentage = threshold ? parseFloat(threshold) : 5;
                const alerts = yield (0, cryptoApi_1.getPriceAlerts)(tokenSymbols, thresholdPercentage);
                return {
                    alerts,
                    threshold: thresholdPercentage,
                    totalAlerts: alerts.length,
                    significantChanges: alerts.filter(a => a.isSignificantChange).length
                };
            }
            catch (error) {
                console.error('Error fetching price alerts:', error);
                reply.code(500).send({ error: 'Failed to fetch price alerts' });
            }
        }));
        // Admin endpoint: Bulk update all user prices
        fastify.post('/admin/bulk-update-prices', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            // Note: In production, add admin role check here
            try {
                const result = yield (0, portfolioService_1.bulkUpdateAllUserPrices)();
                reply.code(200).send({
                    message: 'Bulk price update completed',
                    stats: result
                });
            }
            catch (error) {
                console.error('Error in bulk price update:', error);
                reply.code(500).send({ error: 'Failed to perform bulk update' });
            }
        }));
        // Admin endpoint: Get cache statistics
        fastify.get('/admin/cache-stats', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            try {
                const stats = (0, cryptoApi_1.getCacheStats)();
                return { cacheStats: stats };
            }
            catch (error) {
                console.error('Error fetching cache stats:', error);
                reply.code(500).send({ error: 'Failed to fetch cache stats' });
            }
        }));
        // Admin endpoint: Clear price caches
        fastify.post('/admin/clear-caches', { preHandler: [auth_1.requireAuth] }, (request, reply) => __awaiter(this, void 0, void 0, function* () {
            try {
                (0, cryptoApi_1.clearPriceCaches)();
                reply.code(200).send({
                    message: 'All price caches cleared successfully'
                });
            }
            catch (error) {
                console.error('Error clearing caches:', error);
                reply.code(500).send({ error: 'Failed to clear caches' });
            }
        }));
    });
}
//# sourceMappingURL=portfolio.js.map