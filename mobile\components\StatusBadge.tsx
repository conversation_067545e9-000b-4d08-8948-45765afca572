import { StyleSheet } from 'react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface StatusBadgeProps {
  text: string;
  variant?: 'info' | 'success' | 'warning' | 'error';
}

export function StatusBadge({ text, variant = 'info' }: StatusBadgeProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return {
          backgroundColor: '#d4edda',
          color: '#155724',
        };
      case 'warning':
        return {
          backgroundColor: '#fff3cd',
          color: '#856404',
        };
      case 'error':
        return {
          backgroundColor: '#f8d7da',
          color: '#721c24',
        };
      default:
        return {
          backgroundColor: '#d1ecf1',
          color: '#0c5460',
        };
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <ThemedView style={[styles.badge, { backgroundColor: variantStyles.backgroundColor }]}>
      <ThemedText style={[styles.text, { color: variantStyles.color }]}>
        {text}
      </ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
});
